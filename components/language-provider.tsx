"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

type Language = "sq" | "en"

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const translations = {
  sq: {
    // Navigation
    "nav.dashboard": "Paneli Kryesor",
    "nav.calendar": "Kalendari",
    "nav.book": "Rezervo",
    "nav.activeBookings": "Rezervimet Aktive",
    "nav.cars": "Makinat",
    "nav.damageReports": "Raportet e Dëmeve",
    "nav.wallet": "Portofoli",
    "nav.settings": "Cilësimet",
    "nav.users": "Përdoruesit",
    "nav.clients": "Klientët",

    // Auth
    "auth.login": "Hyrje",
    "auth.register": "Regjistrohu",
    "auth.email": "Email",
    "auth.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "auth.confirmPassword": "<PERSON>n<PERSON><PERSON><PERSON> F<PERSON>kalimin",
    "auth.fullName": "Emri i Plotë",
    "auth.signIn": "Hyr",
    "auth.signUp": "Regjistrohu",
    "auth.noAccount": "Nuk keni llogari?",
    "auth.hasAccount": "Keni tashmë llogari?",

    // Dashboard
    "dashboard.welcome": "Mirë se vini në OR_EL",
    "dashboard.totalCars": "Makinat Gjithsej",
    "dashboard.activeBookings": "Rezervimet Aktive",
    "dashboard.totalRevenue": "Të Ardhurat Gjithsej",
    "dashboard.pendingReports": "Raportet në Pritje",

    // Common
    "common.search": "Kërko...",
    "common.add": "Shto",
    "common.edit": "Ndrysho",
    "common.delete": "Fshi",
    "common.save": "Ruaj",
    "common.cancel": "Anulo",
    "common.loading": "Duke ngarkuar...",
    "common.view": "Shiko",
    "common.filter": "Filtro",
    "common.all": "Të gjitha",
    "common.available": "I disponueshëm",
    "common.rented": "I dhënë me qira",
    "common.maintenance": "Mirëmbajtje",
    "common.active": "Aktiv",
    "common.confirmed": "I konfirmuar",
    "common.pending": "Në pritje",
    "common.completed": "I përfunduar",

    // Booking
    "booking.title": "Krijo Rezervim të Ri",
    "booking.clientName": "Emri i Klientit",
    "booking.clientPhone": "Telefoni i Klientit",
    "booking.selectCar": "Zgjidh Makinën",
    "booking.startDate": "Data e Fillimit",
    "booking.endDate": "Data e Përfundimit",
    "booking.time": "Koha",
    "booking.location": "Vendndodhja e Marrjes",
    "booking.notes": "Shënime Shtesë",
    "booking.summary": "Përmbledhja e Rezervimit",
    "booking.duration": "Kohëzgjatja",
    "booking.dailyRate": "Tarifa Ditore",
    "booking.totalAmount": "Shuma Totale",
    "booking.create": "Krijo Rezervim",
    "booking.searchCars": "Kërko makina sipas markës, modelit...",
    "booking.filterByType": "Filtro sipas llojit",
    "booking.filterByBrand": "Filtro sipas markës",
    "booking.availableCars": "Makinat e Disponueshme",
    "booking.noAvailableCars": "Nuk ka makina të disponueshme",

    // Car Types
    "carType.luxury": "Luksoze",
    "carType.suv": "SUV",
    "carType.sedan": "Sedan",
    "carType.compact": "Kompakte",
    "carType.electric": "Elektrike",
    "carType.sports": "Sportive",

    // Calendar
    "calendar.title": "Kalendari i Rezervimeve",
    "calendar.selectDates": "Kliko për të zgjedhur periudhën e datave",
    "calendar.selectedPeriod": "Periudha e zgjedhur",
    "calendar.clickEndDate": "Kliko datën e përfundimit",
    "calendar.createBooking": "Krijo Rezervim",
    "calendar.clearSelection": "Pastro Zgjedhjen",
    "calendar.todaysBookings": "Rezervimet e Sotme",
    "calendar.noBookingsToday": "Nuk ka rezervime sot",
    "calendar.quickStats": "Statistika të Shpejta",
    "calendar.thisMonth": "Këtë Muaj",
    "calendar.revenue": "Të Ardhurat",
    "calendar.availableCars": "Makinat e Disponueshme",

    // Active Bookings
    "activeBookings.title": "Rezervimet Aktive",
    "activeBookings.subtitle": "Pamje kompakte e të gjitha rezervimeve",
    "activeBookings.searchBookings": "Kërko rezervime...",
    "activeBookings.booking": "REZERVIMI",
    "activeBookings.client": "KLIENTI",
    "activeBookings.car": "MAKINA",
    "activeBookings.dates": "DATAT",
    "activeBookings.location": "VENDNDODHJA",
    "activeBookings.status": "STATUSI",
    "activeBookings.amount": "SHUMA",
    "activeBookings.actions": "VEPRIMET",
    "activeBookings.complete": "Përfundo",

    // Cars
    "cars.title": "Makinat",
    "cars.subtitle": "Menaxhimi kompakt i flotës",
    "cars.allBrands": "Të gjitha Markat",
    "cars.allModels": "Të gjitha Modelet",
    "cars.searchCars": "Kërko makina...",
    "cars.addCar": "Shto Makinë",
    "cars.details": "DETAJET",
    "cars.rate": "TARIFA",

    // Damage Reports
    "damageReports.title": "Raportet e Dëmeve",
    "damageReports.subtitle": "Sistemi kompakt i ndjekjes së dëmeve",
    "damageReports.searchReports": "Kërko raporte...",
    "damageReports.newReport": "Raport i Ri",
    "damageReports.report": "RAPORTI",
    "damageReports.damage": "DËMI",
    "damageReports.severity": "RËNDËSIA",
    "damageReports.cost": "KOSTOJA",
    "damageReports.inRepair": "Në Riparim",
    "damageReports.minor": "I vogël",
    "damageReports.moderate": "Mesatar",
    "damageReports.major": "I madh",

    // Clients
    "clients.title": "Klientët",
    "clients.subtitle": "Menaxho bazën e të dhënave të klientëve",
    "clients.searchClients": "Kërko klientë sipas emrit, email, ose telefonit...",
    "clients.addClient": "Shto Klient",
    "clients.totalClients": "Klientët Gjithsej",
    "clients.activeClients": "Klientët Aktivë",
    "clients.vipClients": "Klientët VIP",
    "clients.avgRating": "Vlerësimi Mesatar",
    "clients.totalBookings": "Rezervimet Gjithsej",
    "clients.totalSpent": "Shuma e Shpenzuar",
    "clients.lastBooking": "Rezervimi i Fundit",
    "clients.preferredCars": "Makinat e Preferuara",
    "clients.viewProfile": "Shiko Profilin",
    "clients.editClient": "Ndrysho Klientin",
    "clients.newBooking": "Rezervim i Ri",
    "clients.joined": "U bashkua",
    "clients.inactive": "Joaktiv",
    "clients.vip": "VIP",

    // Wallet
    "wallet.title": "Portofoli",
    "wallet.subtitle": "Ndiq transaksionet financiare dhe të ardhurat",
    "wallet.totalBalance": "Bilanci Total",
    "wallet.thisMonthIncome": "Të Ardhurat e Këtij Muaji",
    "wallet.pendingPayments": "Pagesat në Pritje",
    "wallet.totalExpenses": "Shpenzimet Totale",
    "wallet.recentTransactions": "Transaksionet e Fundit",
    "wallet.latestActivities": "Aktivitetet e fundit financiare",
    "wallet.quickActions": "Veprime të Shpejta",
    "wallet.addIncome": "Shto të Ardhura",
    "wallet.recordExpense": "Regjistro Shpenzim",
    "wallet.schedulePayment": "Planifiko Pagesë",
    "wallet.monthlySummary": "Përmbledhja Mujore",
    "wallet.totalIncome": "Të Ardhurat Totale",
    "wallet.netProfit": "Fitimi Neto",
    "wallet.export": "Eksporto",

    // Profile & Settings
    "profile.profile": "Profili",
    "profile.settings": "Cilësimet",
    "profile.users": "Përdoruesit",
    "profile.logout": "Dil",
    "profile.notifications": "Njoftimet",
    "profile.administrator": "Administrator",

    // Notifications
    "notifications.title": "Njoftimet",
    "notifications.newBookingRequest": "Kërkesë e re për rezervim",
    "notifications.bookingEnding": "Rezervimi po përfundon",
    "notifications.bookingEnded": "Rezervimi përfundoi",
    "notifications.carMaintenanceDue": "Mirëmbajtja e makinës është e nevojshme",
    "notifications.paymentReceived": "Pagesa u mor",
    "notifications.damageReported": "U raportua dëm",
    "notifications.markAllRead": "Shëno të gjitha si të lexuara",
    "notifications.noNotifications": "Nuk ka njofrime",

    // Time
    "time.now": "tani",
    "time.minutesAgo": "minuta më parë",
    "time.hoursAgo": "orë më parë",
    "time.daysAgo": "ditë më parë",
    "time.days": "ditë",
    "time.hours": "orë",

    // Status
    "status.available": "I disponueshëm",
    "status.rented": "I dhënë me qira",
    "status.maintenance": "Mirëmbajtje",
    "status.active": "Aktiv",
    "status.confirmed": "I konfirmuar",
    "status.pending": "Në pritje",
    "status.completed": "I përfunduar",
    "status.cancelled": "I anuluar",
    "status.paid": "I paguar",
    "status.overdue": "I vonuar",

    // Months
    "month.january": "Janar",
    "month.february": "Shkurt",
    "month.march": "Mars",
    "month.april": "Prill",
    "month.may": "Maj",
    "month.june": "Qershor",
    "month.july": "Korrik",
    "month.august": "Gusht",
    "month.september": "Shtator",
    "month.october": "Tetor",
    "month.november": "Nëntor",
    "month.december": "Dhjetor",

    // Days
    "day.sunday": "E Diel",
    "day.monday": "E Hënë",
    "day.tuesday": "E Martë",
    "day.wednesday": "E Mërkurë",
    "day.thursday": "E Enjte",
    "day.friday": "E Premte",
    "day.saturday": "E Shtunë",
    "day.sun": "Die",
    "day.mon": "Hën",
    "day.tue": "Mar",
    "day.wed": "Mër",
    "day.thu": "Enj",
    "day.fri": "Pre",
    "day.sat": "Sht",
  },
  en: {
    // Navigation
    "nav.dashboard": "Dashboard",
    "nav.calendar": "Calendar",
    "nav.book": "Book",
    "nav.activeBookings": "Active Bookings",
    "nav.cars": "Cars",
    "nav.damageReports": "Damage Reports",
    "nav.wallet": "Wallet",
    "nav.settings": "Settings",
    "nav.users": "Users",
    "nav.clients": "Clients",

    // Auth
    "auth.login": "Login",
    "auth.register": "Register",
    "auth.email": "Email",
    "auth.password": "Password",
    "auth.confirmPassword": "Confirm Password",
    "auth.fullName": "Full Name",
    "auth.signIn": "Sign In",
    "auth.signUp": "Sign Up",
    "auth.noAccount": "Don't have an account?",
    "auth.hasAccount": "Already have an account?",

    // Dashboard
    "dashboard.welcome": "Welcome to OR_EL",
    "dashboard.totalCars": "Total Cars",
    "dashboard.activeBookings": "Active Bookings",
    "dashboard.totalRevenue": "Total Revenue",
    "dashboard.pendingReports": "Pending Reports",

    // Common
    "common.search": "Search...",
    "common.add": "Add",
    "common.edit": "Edit",
    "common.delete": "Delete",
    "common.save": "Save",
    "common.cancel": "Cancel",
    "common.loading": "Loading...",
    "common.view": "View",
    "common.filter": "Filter",
    "common.all": "All",
    "common.available": "Available",
    "common.rented": "Rented",
    "common.maintenance": "Maintenance",
    "common.active": "Active",
    "common.confirmed": "Confirmed",
    "common.pending": "Pending",
    "common.completed": "Completed",

    // Booking
    "booking.title": "Create New Booking",
    "booking.clientName": "Client Name",
    "booking.clientPhone": "Client Phone",
    "booking.selectCar": "Select Car",
    "booking.startDate": "Start Date",
    "booking.endDate": "End Date",
    "booking.time": "Time",
    "booking.location": "Pickup Location",
    "booking.notes": "Additional Notes",
    "booking.summary": "Booking Summary",
    "booking.duration": "Duration",
    "booking.dailyRate": "Daily Rate",
    "booking.totalAmount": "Total Amount",
    "booking.create": "Create Booking",
    "booking.searchCars": "Search cars by make, model...",
    "booking.filterByType": "Filter by type",
    "booking.filterByBrand": "Filter by brand",
    "booking.availableCars": "Available Cars",
    "booking.noAvailableCars": "No available cars found",

    // Car Types
    "carType.luxury": "Luxury",
    "carType.suv": "SUV",
    "carType.sedan": "Sedan",
    "carType.compact": "Compact",
    "carType.electric": "Electric",
    "carType.sports": "Sports",

    // Calendar
    "calendar.title": "Booking Calendar",
    "calendar.selectDates": "Click to select date range",
    "calendar.selectedPeriod": "Selected Period",
    "calendar.clickEndDate": "Click end date",
    "calendar.createBooking": "Create Booking",
    "calendar.clearSelection": "Clear Selection",
    "calendar.todaysBookings": "Today's Bookings",
    "calendar.noBookingsToday": "No bookings today",
    "calendar.quickStats": "Quick Stats",
    "calendar.thisMonth": "This Month",
    "calendar.revenue": "Revenue",
    "calendar.availableCars": "Available Cars",

    // Active Bookings
    "activeBookings.title": "Active Bookings",
    "activeBookings.subtitle": "Compact view of all rental bookings",
    "activeBookings.searchBookings": "Search bookings...",
    "activeBookings.booking": "BOOKING",
    "activeBookings.client": "CLIENT",
    "activeBookings.car": "CAR",
    "activeBookings.dates": "DATES",
    "activeBookings.location": "LOCATION",
    "activeBookings.status": "STATUS",
    "activeBookings.amount": "AMOUNT",
    "activeBookings.actions": "ACTIONS",
    "activeBookings.complete": "Complete",

    // Cars
    "cars.title": "Cars",
    "cars.subtitle": "Compact fleet management",
    "cars.allBrands": "All Brands",
    "cars.allModels": "All Models",
    "cars.searchCars": "Search cars...",
    "cars.addCar": "Add Car",
    "cars.details": "DETAILS",
    "cars.rate": "RATE",

    // Damage Reports
    "damageReports.title": "Damage Reports",
    "damageReports.subtitle": "Compact damage tracking system",
    "damageReports.searchReports": "Search reports...",
    "damageReports.newReport": "New Report",
    "damageReports.report": "REPORT",
    "damageReports.damage": "DAMAGE",
    "damageReports.severity": "SEVERITY",
    "damageReports.cost": "COST",
    "damageReports.inRepair": "In Repair",
    "damageReports.minor": "Minor",
    "damageReports.moderate": "Moderate",
    "damageReports.major": "Major",

    // Clients
    "clients.title": "Clients",
    "clients.subtitle": "Manage your customer database and relationships",
    "clients.searchClients": "Search clients by name, email, or phone...",
    "clients.addClient": "Add Client",
    "clients.totalClients": "Total Clients",
    "clients.activeClients": "Active Clients",
    "clients.vipClients": "VIP Clients",
    "clients.avgRating": "Avg. Rating",
    "clients.totalBookings": "Total Bookings",
    "clients.totalSpent": "Total Spent",
    "clients.lastBooking": "Last booking",
    "clients.preferredCars": "Preferred Cars",
    "clients.viewProfile": "View Profile",
    "clients.editClient": "Edit Client",
    "clients.newBooking": "New Booking",
    "clients.joined": "Joined",
    "clients.inactive": "Inactive",
    "clients.vip": "VIP",

    // Wallet
    "wallet.title": "Wallet",
    "wallet.subtitle": "Track your financial transactions and revenue",
    "wallet.totalBalance": "Total Balance",
    "wallet.thisMonthIncome": "This Month Income",
    "wallet.pendingPayments": "Pending Payments",
    "wallet.totalExpenses": "Total Expenses",
    "wallet.recentTransactions": "Recent Transactions",
    "wallet.latestActivities": "Your latest financial activities",
    "wallet.quickActions": "Quick Actions",
    "wallet.addIncome": "Add Income",
    "wallet.recordExpense": "Record Expense",
    "wallet.schedulePayment": "Schedule Payment",
    "wallet.monthlySummary": "Monthly Summary",
    "wallet.totalIncome": "Total Income",
    "wallet.netProfit": "Net Profit",
    "wallet.export": "Export",

    // Profile & Settings
    "profile.profile": "Profile",
    "profile.settings": "Settings",
    "profile.users": "Users",
    "profile.logout": "Logout",
    "profile.notifications": "Notifications",
    "profile.administrator": "Administrator",

    // Notifications
    "notifications.title": "Notifications",
    "notifications.newBookingRequest": "New booking request",
    "notifications.bookingEnding": "Booking ending soon",
    "notifications.bookingEnded": "Booking has ended",
    "notifications.carMaintenanceDue": "Car maintenance due",
    "notifications.paymentReceived": "Payment received",
    "notifications.damageReported": "Damage reported",
    "notifications.markAllRead": "Mark all as read",
    "notifications.noNotifications": "No notifications",

    // Time
    "time.now": "now",
    "time.minutesAgo": "minutes ago",
    "time.hoursAgo": "hours ago",
    "time.daysAgo": "days ago",
    "time.days": "days",
    "time.hours": "hours",

    // Status
    "status.available": "Available",
    "status.rented": "Rented",
    "status.maintenance": "Maintenance",
    "status.active": "Active",
    "status.confirmed": "Confirmed",
    "status.pending": "Pending",
    "status.completed": "Completed",
    "status.cancelled": "Cancelled",
    "status.paid": "Paid",
    "status.overdue": "Overdue",

    // Months
    "month.january": "January",
    "month.february": "February",
    "month.march": "March",
    "month.april": "April",
    "month.may": "May",
    "month.june": "June",
    "month.july": "July",
    "month.august": "August",
    "month.september": "September",
    "month.october": "October",
    "month.november": "November",
    "month.december": "December",

    // Days
    "day.sunday": "Sunday",
    "day.monday": "Monday",
    "day.tuesday": "Tuesday",
    "day.wednesday": "Wednesday",
    "day.thursday": "Thursday",
    "day.friday": "Friday",
    "day.saturday": "Saturday",
    "day.sun": "Sun",
    "day.mon": "Mon",
    "day.tue": "Tue",
    "day.wed": "Wed",
    "day.thu": "Thu",
    "day.fri": "Fri",
    "day.sat": "Sat",
  },
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>("sq") // Albanian as default

  useEffect(() => {
    const savedLanguage = localStorage.getItem("language") as Language
    if (savedLanguage && (savedLanguage === "en" || savedLanguage === "sq")) {
      setLanguage(savedLanguage)
    }
  }, [])

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang)
    localStorage.setItem("language", lang)
  }

  const t = (key: string): string => {
    return translations[language][key as keyof (typeof translations)[typeof language]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
