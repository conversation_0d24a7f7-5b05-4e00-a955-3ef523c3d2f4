"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Plus, X, Loader2, User, Mail, Phone, MapPin, CreditCard, Save } from "lucide-react"
import { useClients } from "@/hooks/use-clients"
import { Client } from "@/lib/clients"

interface EditClientDialogProps {
  children: React.ReactNode
  client: Client
  onClientUpdated?: () => void
}

interface ClientFormData {
  name: string
  email: string
  phone: string
  address: string
  licenseNumber: string
  status: string
  preferredCars: string[]
  notes: string
}

export function EditClientDialog({ children, client, onClientUpdated }: EditClientDialogProps) {
  const { updateClient } = useClients()
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Form state - initialize with client data
  const [formData, setFormData] = useState<ClientFormData>({
    name: client.name || "",
    email: client.email || "",
    phone: client.phone || "",
    address: client.address || "",
    licenseNumber: client.licenseNumber || "",
    status: client.status || "active",
    preferredCars: client.preferredCars || [],
    notes: client.notes || ""
  })
  
  const [newCarPreference, setNewCarPreference] = useState("")

  // Reset form data when client changes or dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        name: client.name || "",
        email: client.email || "",
        phone: client.phone || "",
        address: client.address || "",
        licenseNumber: client.licenseNumber || "",
        status: client.status || "active",
        preferredCars: client.preferredCars || [],
        notes: client.notes || ""
      })
      setError(null)
    }
  }, [open, client])

  const handleInputChange = (field: keyof ClientFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addCarPreference = () => {
    if (newCarPreference.trim() && !formData.preferredCars.includes(newCarPreference.trim())) {
      setFormData(prev => ({
        ...prev,
        preferredCars: [...prev.preferredCars, newCarPreference.trim()]
      }))
      setNewCarPreference("")
    }
  }

  const removeCarPreference = (carToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      preferredCars: prev.preferredCars.filter(car => car !== carToRemove)
    }))
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError("Name is required")
      return false
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError("Please enter a valid email address")
      return false
    }
    
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Create updated client data object
      const updatedClientData = {
        id: client.id,
        name: formData.name.trim(),
        email: formData.email.trim() || null,
        phone: formData.phone.trim() || null,
        address: formData.address.trim() || null,
        licenseNumber: formData.licenseNumber.trim() || null,
        status: formData.status,
        preferredCars: formData.preferredCars,
        notes: formData.notes.trim() || null,
        // Preserve existing data that shouldn't be changed
        rating: client.rating,
        totalBookings: client.totalBookings,
        totalSpent: client.totalSpent,
        joinDate: client.joinDate,
        lastBooking: client.lastBooking
      }

      await updateClient(client.id, updatedClientData)
      setOpen(false)
      
      // Call the callback to refresh the client list
      if (onClientUpdated) {
        onClientUpdated()
      }
    } catch (err) {
      console.error('Error updating client:', err)
      setError(err instanceof Error ? err.message : "Failed to update client")
    } finally {
      setLoading(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (!newOpen) {
      setError(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Edit Client: {client.name}
          </DialogTitle>
          <DialogDescription>
            Update client information. Changes will be saved to the database.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Full Name *
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter full name"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                </Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="Enter email address"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-phone" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone
                </Label>
                <Input
                  id="edit-phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-address" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Address
              </Label>
              <Input
                id="edit-address"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                placeholder="Enter full address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-licenseNumber" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  License Number
                </Label>
                <Input
                  id="edit-licenseNumber"
                  value={formData.licenseNumber}
                  onChange={(e) => handleInputChange("licenseNumber", e.target.value)}
                  placeholder="Enter driver's license"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="vip">VIP</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Preferred Cars */}
            <div className="space-y-2">
              <Label>Preferred Cars</Label>
              <div className="flex gap-2">
                <Input
                  value={newCarPreference}
                  onChange={(e) => setNewCarPreference(e.target.value)}
                  placeholder="Add car model (e.g., BMW X5)"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCarPreference())}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addCarPreference}
                  disabled={!newCarPreference.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {formData.preferredCars.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.preferredCars.map((car, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {car}
                      <button
                        type="button"
                        onClick={() => removeCarPreference(car)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-notes">Notes</Label>
              <Textarea
                id="edit-notes"
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                placeholder="Additional notes about the client..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Client
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
