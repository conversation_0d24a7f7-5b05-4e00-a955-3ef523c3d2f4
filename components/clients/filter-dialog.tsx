"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Filter, X, Calendar as CalendarIcon, Star, Users, TrendingUp } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { ClientFilters } from "@/lib/clients"

interface FilterDialogProps {
  children: React.ReactNode
  currentFilters: ClientFilters
  onFiltersChange: (filters: ClientFilters) => void
}

interface ExtendedFilters extends ClientFilters {
  minRating?: number
  maxRating?: number
  minBookings?: number
  maxBookings?: number
  minSpent?: number
  maxSpent?: number
  joinDateFrom?: Date
  joinDateTo?: Date
  preferredCar?: string
}

export function FilterDialog({ children, currentFilters, onFiltersChange }: FilterDialogProps) {
  const [open, setOpen] = useState(false)
  const [filters, setFilters] = useState<ExtendedFilters>({
    ...currentFilters,
    minRating: 0,
    maxRating: 5,
    minBookings: 0,
    maxBookings: 100,
    minSpent: 0,
    maxSpent: 10000,
  })

  // Reset filters when dialog opens
  useEffect(() => {
    if (open) {
      setFilters({
        ...currentFilters,
        minRating: 0,
        maxRating: 5,
        minBookings: 0,
        maxBookings: 100,
        minSpent: 0,
        maxSpent: 10000,
      })
    }
  }, [open, currentFilters])

  const handleFilterChange = (key: keyof ExtendedFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const applyFilters = () => {
    // Convert extended filters to basic ClientFilters
    const basicFilters: ClientFilters = {
      search: filters.search,
      status: filters.status,
      limit: filters.limit || 50,
      offset: filters.offset || 0
    }

    onFiltersChange(basicFilters)
    setOpen(false)
  }

  const clearFilters = () => {
    const clearedFilters: ExtendedFilters = {
      search: undefined,
      status: undefined,
      limit: 50,
      offset: 0,
      minRating: 0,
      maxRating: 5,
      minBookings: 0,
      maxBookings: 100,
      minSpent: 0,
      maxSpent: 10000,
      joinDateFrom: undefined,
      joinDateTo: undefined,
      preferredCar: undefined
    }
    setFilters(clearedFilters)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.status && filters.status !== 'all') count++
    if (filters.minRating && filters.minRating > 0) count++
    if (filters.maxRating && filters.maxRating < 5) count++
    if (filters.minBookings && filters.minBookings > 0) count++
    if (filters.maxBookings && filters.maxBookings < 100) count++
    if (filters.minSpent && filters.minSpent > 0) count++
    if (filters.maxSpent && filters.maxSpent < 10000) count++
    if (filters.joinDateFrom) count++
    if (filters.joinDateTo) count++
    if (filters.preferredCar) count++
    return count
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()} active
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Apply advanced filters to find specific clients.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Filters */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Basic Filters</h3>
            
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <Input
                id="search"
                value={filters.search || ""}
                onChange={(e) => handleFilterChange("search", e.target.value || undefined)}
                placeholder="Search by name, email, or phone..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="vip">VIP</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Rating Filter */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Star className="h-4 w-4" />
              Rating Range
            </h3>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Min Rating: {filters.minRating}</span>
                <span>Max Rating: {filters.maxRating}</span>
              </div>
              <div className="px-2">
                <Slider
                  value={[filters.minRating || 0, filters.maxRating || 5]}
                  onValueChange={([min, max]) => {
                    handleFilterChange("minRating", min)
                    handleFilterChange("maxRating", max)
                  }}
                  max={5}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Bookings Filter */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Total Bookings
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minBookings">Min Bookings</Label>
                <Input
                  id="minBookings"
                  type="number"
                  value={filters.minBookings || 0}
                  onChange={(e) => handleFilterChange("minBookings", parseInt(e.target.value) || 0)}
                  min={0}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxBookings">Max Bookings</Label>
                <Input
                  id="maxBookings"
                  type="number"
                  value={filters.maxBookings || 100}
                  onChange={(e) => handleFilterChange("maxBookings", parseInt(e.target.value) || 100)}
                  min={0}
                />
              </div>
            </div>
          </div>

          {/* Spending Filter */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Total Spent (€)</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minSpent">Min Amount</Label>
                <Input
                  id="minSpent"
                  type="number"
                  value={filters.minSpent || 0}
                  onChange={(e) => handleFilterChange("minSpent", parseInt(e.target.value) || 0)}
                  min={0}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxSpent">Max Amount</Label>
                <Input
                  id="maxSpent"
                  type="number"
                  value={filters.maxSpent || 10000}
                  onChange={(e) => handleFilterChange("maxSpent", parseInt(e.target.value) || 10000)}
                  min={0}
                />
              </div>
            </div>
          </div>

          {/* Join Date Filter */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              Join Date Range
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>From Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filters.joinDateFrom && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.joinDateFrom ? format(filters.joinDateFrom, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.joinDateFrom}
                      onSelect={(date) => handleFilterChange("joinDateFrom", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label>To Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filters.joinDateTo && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.joinDateTo ? format(filters.joinDateTo, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.joinDateTo}
                      onSelect={(date) => handleFilterChange("joinDateTo", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* Preferred Car Filter */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Preferred Car</h3>
            
            <div className="space-y-2">
              <Input
                value={filters.preferredCar || ""}
                onChange={(e) => handleFilterChange("preferredCar", e.target.value || undefined)}
                placeholder="Search by preferred car model..."
              />
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Clear All
          </Button>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={applyFilters}>
              Apply Filters
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
