"use client"

import { Calendar, BookOpen, Clock, Car, FileText, Wallet, UserCheck, ChevronRight, LayoutDashboard } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useLanguage } from "@/components/language-provider"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { usePathname } from "next/navigation"

const menuItems = [
  {
    title: "nav.dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
    description: "Overview and analytics",
  },
  {
    title: "nav.calendar",
    url: "/dashboard/calendar",
    icon: Calendar,
    badge: "3",
    description: "View active orders",
  },
  {
    title: "nav.book",
    url: "/dashboard/book",
    icon: BookOpen,
    description: "Create new booking",
  },
  {
    title: "nav.activeBookings",
    url: "/dashboard/active-bookings",
    icon: Clock,
    badge: "12",
    description: "Manage active rentals",
  },
  {
    title: "nav.cars",
    url: "/dashboard/cars",
    icon: Car,
    description: "Fleet management",
  },
  {
    title: "nav.damageReports",
    url: "/dashboard/damage-reports",
    icon: FileText,
    badge: "2",
    description: "Vehicle damage reports",
  },
  {
    title: "nav.wallet",
    url: "/dashboard/wallet",
    icon: Wallet,
    description: "Financial transactions",
  },
  {
    title: "nav.clients",
    url: "/dashboard/clients",
    icon: UserCheck,
    description: "Client database",
  },
]

export function AppSidebar() {
  const { t } = useLanguage()
  const pathname = usePathname()

  return (
    <TooltipProvider delayDuration={0}>
      <Sidebar className="border-r-0 shadow-xl group-data-[collapsible=icon]:shadow-lg">
        <SidebarHeader className="border-b border-sidebar-border/50 bg-gradient-to-r from-sidebar-background to-sidebar-background/80">
          <div className="flex items-center justify-between p-4 group-data-[collapsible=icon]:justify-center">
            <div className="flex items-center gap-3 group-data-[collapsible=icon]:gap-0">
              {/* Modern Logo with Car and Key */}
              <div className="relative">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary via-primary to-primary/80 flex items-center justify-center shadow-lg group-data-[collapsible=icon]:w-10 group-data-[collapsible=icon]:h-10">
                  {/* Car Icon */}
                  <Car className="h-6 w-6 text-white group-data-[collapsible=icon]:h-5 group-data-[collapsible=icon]:w-5" />
                  {/* Key Icon - positioned as overlay */}
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center shadow-md group-data-[collapsible=icon]:w-3 group-data-[collapsible=icon]:h-3 group-data-[collapsible=icon]:-top-0.5 group-data-[collapsible=icon]:-right-0.5">
                    <div className="w-2 h-2 bg-yellow-600 rounded-full group-data-[collapsible=icon]:w-1.5 group-data-[collapsible=icon]:h-1.5"></div>
                  </div>
                </div>
              </div>
              <div className="group-data-[collapsible=icon]:hidden">
                <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  OR_EL
                </h2>
                <p className="text-xs text-muted-foreground font-medium">Rental Management</p>
              </div>
            </div>
            <SidebarTrigger className="modern-button group-data-[collapsible=icon]:hidden" />
          </div>
        </SidebarHeader>

        <SidebarContent className="px-2 py-4">
          <SidebarGroup>
            <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground/80 uppercase tracking-wider px-3 mb-2 group-data-[collapsible=icon]:hidden">
              Navigation
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {menuItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton
                          asChild
                          isActive={pathname === item.url}
                          className="modern-button h-12 px-3 rounded-xl hover:bg-sidebar-accent/80 data-[active=true]:bg-gradient-to-r data-[active=true]:from-primary data-[active=true]:to-primary/90 data-[active=true]:text-white data-[active=true]:shadow-lg group-data-[collapsible=icon]:justify-center"
                        >
                          <Link href={item.url} className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-3">
                              <item.icon className="h-5 w-5 flex-shrink-0" />
                              <span className="font-medium group-data-[collapsible=icon]:hidden">{t(item.title)}</span>
                            </div>
                            <div className="flex items-center gap-2 group-data-[collapsible=icon]:hidden">
                              {item.badge && (
                                <Badge variant="secondary" className="h-5 px-2 text-xs font-semibold">
                                  {item.badge}
                                </Badge>
                              )}
                              <ChevronRight className="h-4 w-4 opacity-50" />
                            </div>
                          </Link>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="group-data-[collapsible=expanded]:hidden">
                        <div className="space-y-1">
                          <p className="font-medium">{t(item.title)}</p>
                          <p className="text-xs text-muted-foreground">{item.description}</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter className="border-t border-sidebar-border/50 p-4">
          <div className="text-center text-xs text-muted-foreground group-data-[collapsible=icon]:hidden">
            © 2024 OR_EL Rentals
          </div>
        </SidebarFooter>
      </Sidebar>
    </TooltipProvider>
  )
}
