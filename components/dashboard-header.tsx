"use client"

import { useState } from "react"
import { Search, Bell, Plus, Filter, User, <PERSON>tings, Users, LogOut, UserCircle, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useLanguage } from "@/components/language-provider"
import { ThemeToggle } from "@/components/theme-toggle"
import { LanguageToggle } from "@/components/language-toggle"
import { FloatingChat } from "@/components/floating-chat"
import Link from "next/link"

interface DashboardHeaderProps {
  title: string
  subtitle?: string
  showSearch?: boolean
  showActions?: boolean
}

export function DashboardHeader({ title, subtitle, showSearch = true, showActions = true }: DashboardHeaderProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [isChatOpen, setIsChatOpen] = useState(false)
  const { t } = useLanguage()

  const notifications = [
    {
      id: 1,
      title: t("notifications.newBookingRequest"),
      time: "2 " + t("time.minutesAgo"),
      type: "booking",
      read: false,
    },
    {
      id: 2,
      title: t("notifications.bookingEnding"),
      time: "1 " + t("time.hoursAgo"),
      type: "warning",
      read: false,
    },
    {
      id: 3,
      title: t("notifications.paymentReceived"),
      time: "3 " + t("time.hoursAgo"),
      type: "payment",
      read: true,
    },
    {
      id: 4,
      title: t("notifications.carMaintenanceDue"),
      time: "1 " + t("time.daysAgo"),
      type: "maintenance",
      read: false,
    },
    {
      id: 5,
      title: t("notifications.bookingEnded"),
      time: "2 " + t("time.daysAgo"),
      type: "info",
      read: true,
    },
  ]

  const unreadCount = notifications.filter((n) => !n.read).length

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "booking":
        return "🚗"
      case "warning":
        return "⚠️"
      case "payment":
        return "💰"
      case "maintenance":
        return "🔧"
      default:
        return "ℹ️"
    }
  }

  return (
    <>
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-16 items-center justify-between px-6">
          {/* Left Section - Title and Breadcrumb */}
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                {title}
              </h1>
              {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
            </div>
          </div>

          {/* Center Section - Search */}
          {showSearch && (
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("common.search")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-muted/50 border-0 focus-visible:ring-1"
                />
              </div>
            </div>
          )}

          {/* Right Section - Actions */}
          <div className="flex items-center gap-3">
            {showActions && (
              <>
                <Button variant="outline" size="sm" className="modern-button">
                  <Plus className="h-4 w-4 mr-2" />
                  {t("common.add")}
                </Button>

                <Button variant="outline" size="icon" className="modern-button">
                  <Filter className="h-4 w-4" />
                </Button>
              </>
            )}

            {/* Chat Button */}
            <Button
              variant="outline"
              size="icon"
              className="modern-button relative"
              onClick={() => setIsChatOpen(!isChatOpen)}
            >
              <MessageCircle className="h-4 w-4" />
            </Button>

            {/* Notifications */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon" className="modern-button relative">
                  <Bell className="h-4 w-4" />
                  {unreadCount > 0 && (
                    <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel className="flex items-center justify-between">
                  {t("notifications.title")}
                  {unreadCount > 0 && (
                    <Button variant="ghost" size="sm" className="text-xs">
                      {t("notifications.markAllRead")}
                    </Button>
                  )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {notifications.length > 0 ? (
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.map((notification) => (
                      <DropdownMenuItem key={notification.id} className="flex items-start p-4 cursor-pointer">
                        <div className="flex items-start gap-3 w-full">
                          <span className="text-lg">{getNotificationIcon(notification.type)}</span>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p
                                className={`text-sm font-medium ${!notification.read ? "text-foreground" : "text-muted-foreground"}`}
                              >
                                {notification.title}
                              </p>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2"></div>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">{notification.time}</p>
                          </div>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-muted-foreground">{t("notifications.noNotifications")}</div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex items-center gap-2">
              <LanguageToggle />
              <ThemeToggle />
            </div>

            {/* User Profile Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                  <Avatar className="h-10 w-10 ring-2 ring-primary/20">
                    <AvatarImage src="/placeholder.svg?height=40&width=40" />
                    <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-white">
                      <User className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">John Doe</p>
                    <p className="text-xs leading-none text-muted-foreground">{t("profile.administrator")}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/profile" className="flex items-center">
                    <UserCircle className="mr-2 h-4 w-4" />
                    <span>{t("profile.profile")}</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/users" className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    <span>{t("profile.users")}</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings" className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>{t("profile.settings")}</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/auth/login" className="flex items-center text-red-600 dark:text-red-400">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{t("profile.logout")}</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      {/* Chat Interface */}
      <FloatingChat isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
    </>
  )
}
