"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/components/language-provider"
import { Languages } from "lucide-react"

export function LanguageToggle() {
  const { language, setLanguage } = useLanguage()

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={() => setLanguage(language === "sq" ? "en" : "sq")}
      className="relative"
    >
      <Languages className="h-[1.2rem] w-[1.2rem]" />
      <span className="absolute -top-1 -right-1 text-xs font-bold bg-primary text-white rounded-full w-4 h-4 flex items-center justify-center">
        {language.toUpperCase()}
      </span>
      <span className="sr-only">Toggle language</span>
    </Button>
  )
}
