# Database Setup Guide

This guide will help you set up and connect to your PostgreSQL database for the OR_EL Rental application.

## Database Configuration

Your PostgreSQL database is already configured with the following credentials:

- **Host**: **********
- **Port**: 5432
- **Database**: rental_or_el_db
- **Username**: administrator
- **Password**: or_el_pass2025

## Environment Variables

The `.env` file has been created with your database configuration. The following environment variables are available:

```env
# Main database connection string
DATABASE_URL="*********************************************************/rental_or_el_db"

# Individual database variables
DB_HOST=**********
DB_PORT=5432
DB_USER=administrator
DB_PASSWORD=or_el_pass2025
DB_NAME=rental_or_el_db

# Alternative connection for local Docker
DATABASE_URL_DOCKER="postgresql://administrator:or_el_pass2025@localhost:5432/rental_or_el_db"
```

## Installation

## 🚨 NPM Issues? No Problem!

If you're experiencing npm dependency conflicts, you have several options:

### Option 1: Automated Fix Script

Run the automated fix script:

```bash
./fix-npm.sh
```

This script will:
- Clean all caches and node_modules
- Try multiple installation methods
- Attempt to install PostgreSQL driver
- Provide detailed troubleshooting info

### Option 2: Manual Dependency Resolution

```bash
# Complete cleanup
rm -rf node_modules package-lock.json
npm cache clean --force

# Try different installation methods
npm install --legacy-peer-deps --force
# OR
npm install --no-optional --legacy-peer-deps
# OR
npm install --legacy-peer-deps --no-audit
```

### Option 3: Alternative Package Managers

```bash
# Install yarn and try
npm install -g yarn
yarn install
yarn add pg @types/pg

# OR install pnpm and try
npm install -g pnpm
pnpm install
pnpm add pg @types/pg
```

### Option 4: Use Without Database (Recommended for now)

Your app works perfectly without the PostgreSQL driver! You can:

1. **Continue development** - All features work except database operations
2. **Test database config** - Visit `http://localhost:3000/api/test-db` to see your setup
3. **Install pg later** - When npm issues are resolved

### Option 5: Node.js Version Issues

```bash
# Check current version
node --version
npm --version

# Try different Node.js versions
nvm install 18
nvm use 18
npm install

# OR
nvm install 20
nvm use 20
npm install
```

## Usage

### Basic Connection Test

```typescript
import { testConnection } from './lib/database'

// Test the database connection
const isConnected = await testConnection()
if (isConnected) {
  console.log('Database connected successfully!')
}
```

### Execute Queries

```typescript
import { query } from './lib/database'

// Execute a simple query
const result = await query('SELECT NOW()')
console.log('Current time:', result.rows[0].now)

// Execute a query with parameters
const users = await query('SELECT * FROM users WHERE active = $1', [true])
console.log('Active users:', users.rows)
```

### Using the Database Pool

```typescript
import { getPool } from './lib/database'

const pool = getPool()
const client = await pool.connect()

try {
  const result = await client.query('SELECT * FROM cars')
  console.log('Cars:', result.rows)
} finally {
  client.release()
}
```

## Database Schema

You can create tables for your rental car management system. Here are some suggested tables:

### Cars Table
```sql
CREATE TABLE cars (
  id SERIAL PRIMARY KEY,
  make VARCHAR(50) NOT NULL,
  model VARCHAR(50) NOT NULL,
  year INTEGER NOT NULL,
  plate VARCHAR(20) UNIQUE NOT NULL,
  daily_rate DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'available',
  fuel_type VARCHAR(20),
  seats INTEGER,
  transmission VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Clients Table
```sql
CREATE TABLE clients (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(20),
  address TEXT,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Bookings Table
```sql
CREATE TABLE bookings (
  id SERIAL PRIMARY KEY,
  booking_number VARCHAR(20) UNIQUE NOT NULL,
  client_id INTEGER REFERENCES clients(id),
  car_id INTEGER REFERENCES cars(id),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  total_amount DECIMAL(10,2),
  status VARCHAR(20) DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Connection Troubleshooting

If you encounter connection issues:

1. **Check if PostgreSQL is running**:
   ```bash
   docker ps
   ```

2. **Verify network connectivity**:
   ```bash
   ping **********
   ```

3. **Test connection manually**:
   ```bash
   psql -h ********** -p 5432 -U administrator -d rental_or_el_db
   ```

4. **Check firewall settings**: Ensure port 5432 is accessible

## Security Notes

- The `.env` file is already added to `.gitignore` to prevent committing sensitive credentials
- Use `.env.example` as a template for other developers
- Consider using connection pooling for production environments
- Regularly update passwords and use strong authentication methods

## Next Steps

1. Install the PostgreSQL driver: `npm install pg @types/pg`
2. Test the database connection using the provided utilities
3. Create your database schema
4. Start building your application with database integration

For more advanced features, consider using an ORM like Prisma or TypeORM with the provided connection configuration.
