// Client data types and API functions

export interface Client {
  id: number
  name: string
  email?: string
  phone?: string
  address?: string
  licenseNumber?: string
  status: 'active' | 'vip' | 'inactive'
  joinDate: string
  rating: number
  preferredCars: string[]
  avatar?: string
  totalBookings: number
  totalSpent: number
  lastBooking?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateClientData {
  name: string
  email?: string
  phone?: string
  address?: string
  licenseNumber?: string
  status?: 'active' | 'vip' | 'inactive'
  rating?: number
  preferredCars?: string[]
  avatar?: string
  notes?: string
}

export interface UpdateClientData extends Partial<CreateClientData> {}

export interface ClientsResponse {
  success: boolean
  data: {
    clients: Client[]
    pagination: {
      total: number
      limit: number
      offset: number
      hasMore: boolean
    }
  }
}

export interface ClientResponse {
  success: boolean
  data: Client
}

export interface ClientFilters {
  status?: string
  search?: string
  limit?: number
  offset?: number
}

// API Functions
export class ClientsAPI {
  private static baseUrl = '/api/clients'

  // Fetch all clients with optional filters
  static async getClients(filters: ClientFilters = {}): Promise<ClientsResponse> {
    const params = new URLSearchParams()
    
    if (filters.status) params.append('status', filters.status)
    if (filters.search) params.append('search', filters.search)
    if (filters.limit) params.append('limit', filters.limit.toString())
    if (filters.offset) params.append('offset', filters.offset.toString())

    const url = `${this.baseUrl}?${params.toString()}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch clients: ${response.statusText}`)
    }

    return response.json()
  }

  // Fetch a specific client by ID
  static async getClient(id: number): Promise<ClientResponse> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch client: ${response.statusText}`)
    }

    return response.json()
  }

  // Create a new client
  static async createClient(clientData: CreateClientData): Promise<ClientResponse> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(clientData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `Failed to create client: ${response.statusText}`)
    }

    return response.json()
  }

  // Update an existing client
  static async updateClient(id: number, clientData: UpdateClientData): Promise<ClientResponse> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(clientData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `Failed to update client: ${response.statusText}`)
    }

    return response.json()
  }

  // Delete a client
  static async deleteClient(id: number): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `Failed to delete client: ${response.statusText}`)
    }

    return response.json()
  }
}

// Utility functions
export const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'vip':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'inactive':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
      return '✓'
    case 'vip':
      return '⭐'
    case 'inactive':
      return '○'
    default:
      return '○'
  }
}

// Format currency for display
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'EUR',
  }).format(amount)
}

// Format date for display
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

// Generate client initials for avatar
export const getClientInitials = (name: string): string => {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
