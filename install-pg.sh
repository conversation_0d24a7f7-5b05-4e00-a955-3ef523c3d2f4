#!/bin/bash

echo "🔧 Installing PostgreSQL driver for OR_EL Rental..."
echo ""

# Function to try different installation methods
install_pg() {
    echo "📦 Attempting to install pg and @types/pg..."
    
    # Method 1: npm with legacy peer deps
    echo "🔄 Trying: npm install pg @types/pg --legacy-peer-deps"
    if npm install pg @types/pg --legacy-peer-deps; then
        echo "✅ Successfully installed with npm --legacy-peer-deps"
        return 0
    fi
    
    echo "❌ npm failed, trying yarn..."
    
    # Method 2: yarn
    echo "🔄 Trying: yarn add pg @types/pg"
    if command -v yarn &> /dev/null && yarn add pg @types/pg; then
        echo "✅ Successfully installed with yarn"
        return 0
    fi
    
    echo "❌ yarn failed, trying pnpm..."
    
    # Method 3: pnpm
    echo "🔄 Trying: pnpm add pg @types/pg"
    if command -v pnpm &> /dev/null && pnpm add pg @types/pg; then
        echo "✅ Successfully installed with pnpm"
        return 0
    fi
    
    echo "❌ All package managers failed"
    return 1
}

# Clean npm cache first
echo "🧹 Cleaning npm cache..."
npm cache clean --force

# Try installation
if install_pg; then
    echo ""
    echo "🎉 PostgreSQL driver installed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Test your database connection: curl http://localhost:3000/api/test-db"
    echo "2. Start using database functions in your code"
    echo "3. Check DATABASE_SETUP.md for usage examples"
    echo ""
    echo "🔗 Your database connection string:"
    echo "***********************************************/rental_or_el_db"
else
    echo ""
    echo "❌ Installation failed with all methods"
    echo ""
    echo "🛠️  Manual installation options:"
    echo ""
    echo "1. Add to package.json manually:"
    echo '   "dependencies": { "pg": "^8.11.3" }'
    echo '   "devDependencies": { "@types/pg": "^8.10.9" }'
    echo "   Then run: npm install --legacy-peer-deps"
    echo ""
    echo "2. Try installing one at a time:"
    echo "   npm install pg --legacy-peer-deps"
    echo "   npm install @types/pg --save-dev --legacy-peer-deps"
    echo ""
    echo "3. Check for dependency conflicts:"
    echo "   npm ls"
    echo "   npm audit fix"
fi

echo ""
echo "📚 For more help, see DATABASE_SETUP.md"
