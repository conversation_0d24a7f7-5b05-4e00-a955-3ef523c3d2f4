import { NextRequest, NextResponse } from 'next/server'
import { dbConfig, connectionString, checkPgAvailability, getInstallationStatus } from '@/lib/database'

export async function GET() {
  try {
    const status = getInstallationStatus()

    if (status.pgInstalled) {
      // If pg is installed, try to test connection
      try {
        const { testConnection, query } = await import('@/lib/database')
        const isConnected = await testConnection()

        if (isConnected) {
          const result = await query('SELECT NOW() as current_time, version() as postgres_version')

          return NextResponse.json({
            success: true,
            message: 'Database connection successful!',
            data: {
              connected: true,
              currentTime: result.rows[0].current_time,
              postgresVersion: result.rows[0].postgres_version,
              config: {
                host: dbConfig.host,
                port: dbConfig.port,
                database: dbConfig.database,
                user: dbConfig.user,
                ssl: dbConfig.ssl
              }
            }
          })
        } else {
          return NextResponse.json({
            success: false,
            message: 'PostgreSQL driver installed but connection failed',
            data: status
          }, { status: 500 })
        }
      } catch (connectionError) {
        return NextResponse.json({
          success: false,
          message: 'Database connection error',
          error: connectionError instanceof Error ? connectionError.message : 'Unknown error',
          data: status
        }, { status: 500 })
      }
    } else {
      // If pg is not installed, return helpful information
      return NextResponse.json({
        success: false,
        message: 'PostgreSQL driver not installed',
        note: 'Your database configuration is ready, but the PostgreSQL driver needs to be installed',
        data: {
          ...status,
          config: {
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database,
            user: dbConfig.user,
            ssl: dbConfig.ssl
          },
          environment: {
            DB_HOST: process.env.DB_HOST,
            DB_PORT: process.env.DB_PORT,
            DB_NAME: process.env.DB_NAME,
            DB_USER: process.env.DB_USER,
            DATABASE_URL_SET: !!process.env.DATABASE_URL
          },
          solutions: [
            'Run: ./fix-npm.sh (automated fix)',
            'Run: npm install pg @types/pg --legacy-peer-deps',
            'Try: yarn add pg @types/pg',
            'Try: pnpm add pg @types/pg',
            'Check Node.js version compatibility'
          ]
        }
      })
    }
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      {
        error: 'Database test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        data: getInstallationStatus()
      },
      { status: 500 }
    )
  }
}

// POST endpoint - test database with a simple query
export async function POST() {
  const status = getInstallationStatus()

  if (!status.pgInstalled) {
    return NextResponse.json({
      success: false,
      message: 'PostgreSQL driver not installed',
      data: status
    }, { status: 400 })
  }

  try {
    const { query } = await import('@/lib/database')
    const result = await query('SELECT 1 as test, NOW() as timestamp')

    return NextResponse.json({
      success: true,
      message: 'Database query test successful!',
      data: {
        testResult: result.rows[0],
        rowCount: result.rowCount,
        connected: true
      }
    })
  } catch (error) {
    console.error('Database POST test error:', error)
    return NextResponse.json({
      success: false,
      message: 'Database query test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      data: status
    }, { status: 500 })
  }
}
