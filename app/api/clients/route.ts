import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - Fetch all clients with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let sql = `
      SELECT 
        id,
        full_name as name,
        email,
        phone,
        address,
        license_number,
        status,
        join_date as "joinDate",
        rating,
        preferred_cars as "preferredCars",
        avatar,
        total_bookings as "totalBookings",
        total_spent as "totalSpent",
        last_booking as "lastBooking",
        notes,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM clients
      WHERE 1=1
    `
    
    const params: any[] = []
    let paramIndex = 1

    // Filter by status
    if (status && status !== 'all') {
      sql += ` AND status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // Search functionality
    if (search) {
      sql += ` AND (
        full_name ILIKE $${paramIndex} OR 
        email ILIKE $${paramIndex} OR 
        phone ILIKE $${paramIndex}
      )`
      params.push(`%${search}%`)
      paramIndex++
    }

    // Order by most recent first
    sql += ` ORDER BY created_at DESC`
    
    // Add pagination
    sql += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    params.push(limit, offset)

    const result = await query(sql, params)

    // Get total count for pagination
    let countSql = `SELECT COUNT(*) as total FROM clients WHERE 1=1`
    const countParams: any[] = []
    let countParamIndex = 1

    if (status && status !== 'all') {
      countSql += ` AND status = $${countParamIndex}`
      countParams.push(status)
      countParamIndex++
    }

    if (search) {
      countSql += ` AND (
        full_name ILIKE $${countParamIndex} OR 
        email ILIKE $${countParamIndex} OR 
        phone ILIKE $${countParamIndex}
      )`
      countParams.push(`%${search}%`)
    }

    const countResult = await query(countSql, countParams)
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: {
        clients: result.rows,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    })

  } catch (error) {
    console.error('Error fetching clients:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch clients',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create a new client
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      email,
      phone,
      address,
      licenseNumber,
      status = 'active',
      rating = 0.0,
      preferredCars = [],
      avatar,
      notes
    } = body

    // Validation
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'Name is required' },
        { status: 400 }
      )
    }

    const sql = `
      INSERT INTO clients (
        full_name, email, phone, address, license_number, 
        status, rating, preferred_cars, avatar, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING 
        id,
        full_name as name,
        email,
        phone,
        address,
        license_number as "licenseNumber",
        status,
        join_date as "joinDate",
        rating,
        preferred_cars as "preferredCars",
        avatar,
        total_bookings as "totalBookings",
        total_spent as "totalSpent",
        last_booking as "lastBooking",
        notes,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `

    const params = [
      name,
      email,
      phone,
      address,
      licenseNumber,
      status,
      rating,
      JSON.stringify(preferredCars),
      avatar,
      notes
    ]

    const result = await query(sql, params)

    return NextResponse.json({
      success: true,
      message: 'Client created successfully',
      data: result.rows[0]
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating client:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create client',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
