"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { Clock, Search, Car, Phone, MapPin, Calendar, Eye, Edit, CheckCircle, MoreHorizontal } from "lucide-react"
import { useState } from "react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function ActiveBookingsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const bookings = [
    {
      id: 1,
      bookingNumber: "BK-001",
      client: { name: "<PERSON>", phone: "+355 69 123 4567" },
      car: { make: "BMW", model: "X5", plate: "ABC-123" },
      dates: "Jan 15-18",
      time: "09:00-17:00",
      location: "Airport",
      status: "active",
      amount: 360,
      days: 3,
    },
    {
      id: 2,
      bookingNumber: "BK-002",
      client: { name: "Jane Smith", phone: "+355 69 987 6543" },
      car: { make: "Mercedes", model: "C-Class", plate: "DEF-456" },
      dates: "Jan 16-20",
      time: "10:00-18:00",
      location: "City Center",
      status: "confirmed",
      amount: 400,
      days: 4,
    },
    {
      id: 3,
      bookingNumber: "BK-003",
      client: { name: "Mike Johnson", phone: "+355 69 555 1234" },
      car: { make: "Audi", model: "A4", plate: "GHI-789" },
      dates: "Jan 17-19",
      time: "14:00-20:00",
      location: "Hotel",
      status: "active",
      amount: 220,
      days: 2,
    },
    {
      id: 4,
      bookingNumber: "BK-004",
      client: { name: "Sarah Wilson", phone: "+355 69 444 5678" },
      car: { make: "Toyota", model: "Camry", plate: "JKL-012" },
      dates: "Jan 18-21",
      time: "12:00-16:00",
      location: "Downtown",
      status: "pending",
      amount: 240,
      days: 3,
    },
    {
      id: 5,
      bookingNumber: "BK-005",
      client: { name: "David Brown", phone: "+355 69 333 9876" },
      car: { make: "VW", model: "Golf", plate: "MNO-345" },
      dates: "Jan 19-22",
      time: "08:00-20:00",
      location: "Airport",
      status: "confirmed",
      amount: 280,
      days: 3,
    },
    {
      id: 6,
      bookingNumber: "BK-006",
      client: { name: "Lisa Anderson", phone: "+355 69 222 5432" },
      car: { make: "Ford", model: "Focus", plate: "PQR-678" },
      dates: "Jan 20-23",
      time: "09:00-18:00",
      location: "City Center",
      status: "active",
      amount: 195,
      days: 3,
    },
  ]

  const filteredBookings = bookings.filter((booking) => {
    const matchesSearch =
      booking.client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.car.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.car.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.bookingNumber.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || booking.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "confirmed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader
        title={t("nav.activeBookings")}
        subtitle="Compact view of all rental bookings"
        showActions={false}
      />

      <div className="flex-1 p-6 space-y-4 bg-gradient-to-br from-background to-muted/20">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { label: "Active", value: "12", color: "text-green-600" },
            { label: "Confirmed", value: "8", color: "text-blue-600" },
            { label: "Revenue", value: "€2,840", color: "text-purple-600" },
            { label: "Pending", value: "4", color: "text-yellow-600" },
          ].map((stat, index) => (
            <Card key={index} className="p-3 border-0 shadow-sm">
              <div className="text-center">
                <p className={`text-xl font-bold ${stat.color}`}>{stat.value}</p>
                <p className="text-xs text-muted-foreground">{stat.label}</p>
              </div>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <div className="flex items-center gap-3">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search bookings..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9 bg-muted/50 border-0"
            />
          </div>
          <div className="flex gap-2">
            {["all", "active", "confirmed", "pending"].map((status) => (
              <Button
                key={status}
                variant={statusFilter === status ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter(status)}
                className="h-9 px-3 text-xs capitalize"
              >
                {status}
              </Button>
            ))}
          </div>
        </div>

        {/* Compact Bookings Table */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Active Bookings ({filteredBookings.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/30">
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">BOOKING</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">CLIENT</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">CAR</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">DATES</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">LOCATION</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">STATUS</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">AMOUNT</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">ACTIONS</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBookings.map((booking, index) => (
                    <tr
                      key={booking.id}
                      className={`border-b hover:bg-muted/20 transition-colors ${
                        index % 2 === 0 ? "bg-background" : "bg-muted/10"
                      }`}
                    >
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm">{booking.bookingNumber}</p>
                          <p className="text-xs text-muted-foreground">{booking.time}</p>
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm">{booking.client.name}</p>
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {booking.client.phone}
                          </p>
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm flex items-center gap-1">
                            <Car className="h-3 w-3" />
                            {booking.car.make} {booking.car.model}
                          </p>
                          <p className="text-xs text-muted-foreground">{booking.car.plate}</p>
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {booking.dates}
                          </p>
                          <p className="text-xs text-muted-foreground">{booking.days} days</p>
                        </div>
                      </td>
                      <td className="p-3">
                        <p className="text-sm flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          {booking.location}
                        </p>
                      </td>
                      <td className="p-3">
                        <Badge className={`${getStatusColor(booking.status)} text-xs`}>{booking.status}</Badge>
                      </td>
                      <td className="p-3">
                        <p className="font-bold text-sm text-primary">€{booking.amount}</p>
                      </td>
                      <td className="p-3">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Complete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
