"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { CalendarIcon, ChevronLeft, ChevronRight, Plus, Filter, Clock, User } from "lucide-react"
import { useState } from "react"

export default function CalendarPage() {
  const { t } = useLanguage()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedStartDate, setSelectedStartDate] = useState<number | null>(null)
  const [selectedEndDate, setSelectedEndDate] = useState<number | null>(null)
  const [isBookingDialogOpen, setIsBookingDialogOpen] = useState(false)
  const [newBooking, setNewBooking] = useState({
    client: "",
    car: "",
    time: "",
    location: "",
  })

  const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay()

  const monthNames = [
    t("month.january"),
    t("month.february"),
    t("month.march"),
    t("month.april"),
    t("month.may"),
    t("month.june"),
    t("month.july"),
    t("month.august"),
    t("month.september"),
    t("month.october"),
    t("month.november"),
    t("month.december"),
  ]

  const dayNames = [t("day.sun"), t("day.mon"), t("day.tue"), t("day.wed"), t("day.thu"), t("day.fri"), t("day.sat")]

  const availableCars = [
    { id: "1", name: "BMW X5", rate: 120 },
    { id: "2", name: "Mercedes C-Class", rate: 100 },
    { id: "3", name: "Audi A4", rate: 110 },
    { id: "4", name: "Toyota Camry", rate: 80 },
  ]

  const bookings = [
    {
      id: 1,
      date: 15,
      car: "BMW X5",
      client: "John Doe",
      time: "09:00-17:00",
      status: t("status.confirmed"),
    },
    {
      id: 2,
      date: 18,
      car: "Mercedes C-Class",
      client: "Jane Smith",
      time: "10:00-18:00",
      status: t("status.pending"),
    },
    {
      id: 3,
      date: 22,
      car: "Audi A4",
      client: "Mike Johnson",
      time: "14:00-20:00",
      status: t("status.confirmed"),
    },
  ]

  const navigateMonth = (direction: number) => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + direction, 1))
    setSelectedStartDate(null)
    setSelectedEndDate(null)
  }

  const handleDateClick = (day: number) => {
    if (!selectedStartDate || (selectedStartDate && selectedEndDate)) {
      // Start new selection
      setSelectedStartDate(day)
      setSelectedEndDate(null)
    } else if (selectedStartDate && !selectedEndDate) {
      // Set end date
      if (day >= selectedStartDate) {
        setSelectedEndDate(day)
        setIsBookingDialogOpen(true)
      } else {
        setSelectedStartDate(day)
        setSelectedEndDate(null)
      }
    }
  }

  const getBookingsForDate = (date: number) => {
    return bookings.filter((booking) => booking.date === date)
  }

  const isDateInRange = (day: number) => {
    if (!selectedStartDate) return false
    if (!selectedEndDate) return day === selectedStartDate
    return day >= selectedStartDate && day <= selectedEndDate
  }

  const isDateSelected = (day: number) => {
    return day === selectedStartDate || day === selectedEndDate
  }

  const handleCreateBooking = () => {
    console.log("Creating booking:", {
      ...newBooking,
      startDate: selectedStartDate,
      endDate: selectedEndDate,
    })
    setIsBookingDialogOpen(false)
    setNewBooking({ client: "", car: "", time: "", location: "" })
    setSelectedStartDate(null)
    setSelectedEndDate(null)
  }

  const calculateDuration = () => {
    if (selectedStartDate && selectedEndDate) {
      return selectedEndDate - selectedStartDate + 1
    }
    return 0
  }

  const calculateTotal = () => {
    const duration = calculateDuration()
    const car = availableCars.find((c) => c.id === newBooking.car)
    return duration > 0 && car ? duration * car.rate : 0
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader title={t("nav.calendar")} subtitle={t("calendar.selectDates")} showActions={false} />

      <div className="flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20">
        {/* Calendar Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon" onClick={() => navigateMonth(-1)} className="modern-button">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-2xl font-bold min-w-[200px] text-center">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h2>
              <Button variant="outline" size="icon" onClick={() => navigateMonth(1)} className="modern-button">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {(selectedStartDate || selectedEndDate) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedStartDate(null)
                  setSelectedEndDate(null)
                }}
                className="modern-button"
              >
                {t("calendar.clearSelection")}
              </Button>
            )}
            <Button variant="outline" size="sm" className="modern-button">
              <Filter className="h-4 w-4 mr-2" />
              {t("common.filter")}
            </Button>
          </div>
        </div>

        {/* Selection Info */}
        {selectedStartDate && (
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <CalendarIcon className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium">
                      {selectedEndDate
                        ? `${t("calendar.selectedPeriod")}: ${selectedStartDate} - ${selectedEndDate} (${calculateDuration()} ${t("time.days")})`
                        : `${t("booking.startDate")}: ${selectedStartDate} (${t("calendar.clickEndDate")})`}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {selectedEndDate ? t("calendar.createBooking") : t("calendar.clickEndDate")}
                    </p>
                  </div>
                </div>
                {selectedEndDate && (
                  <Button onClick={() => setIsBookingDialogOpen(true)} className="gradient-bg">
                    <Plus className="h-4 w-4 mr-2" />
                    {t("calendar.createBooking")}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Calendar */}
          <div className="lg:col-span-3">
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  {t("calendar.title")} - {t("calendar.selectDates")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Days Header */}
                <div className="grid grid-cols-7 gap-2 mb-4">
                  {dayNames.map((day) => (
                    <div
                      key={day}
                      className="p-3 text-center font-semibold text-muted-foreground bg-muted/30 rounded-lg"
                    >
                      {day}
                    </div>
                  ))}
                </div>

                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-2">
                  {/* Empty cells for days before month starts */}
                  {Array.from({ length: firstDayOfMonth }, (_, i) => (
                    <div key={`empty-${i}`} className="p-2 h-24"></div>
                  ))}

                  {/* Days of the month */}
                  {Array.from({ length: daysInMonth }, (_, i) => {
                    const day = i + 1
                    const dayBookings = getBookingsForDate(day)
                    const isToday =
                      day === new Date().getDate() &&
                      currentDate.getMonth() === new Date().getMonth() &&
                      currentDate.getFullYear() === new Date().getFullYear()
                    const inRange = isDateInRange(day)
                    const isSelected = isDateSelected(day)

                    return (
                      <div
                        key={day}
                        className={`p-2 h-24 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                          isToday
                            ? "bg-primary/10 border-primary"
                            : isSelected
                              ? "bg-primary border-primary text-white"
                              : inRange
                                ? "bg-primary/20 border-primary/50"
                                : dayBookings.length > 0
                                  ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
                                  : "hover:bg-muted/50"
                        }`}
                        onClick={() => handleDateClick(day)}
                      >
                        <div
                          className={`font-semibold text-sm ${isSelected ? "text-white" : isToday ? "text-primary" : ""}`}
                        >
                          {day}
                        </div>
                        <div className="mt-1 space-y-1">
                          {dayBookings.slice(0, 2).map((booking, index) => (
                            <div
                              key={index}
                              className={`text-xs px-1 py-0.5 rounded truncate ${
                                isSelected ? "bg-white/20 text-white" : "bg-primary/80 text-white"
                              }`}
                            >
                              {booking.car}
                            </div>
                          ))}
                          {dayBookings.length > 2 && (
                            <div className={`text-xs ${isSelected ? "text-white/80" : "text-muted-foreground"}`}>
                              +{dayBookings.length - 2} more
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">{t("calendar.todaysBookings")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getBookingsForDate(new Date().getDate()).length === 0 ? (
                    <p className="text-muted-foreground text-center py-4 text-sm">{t("calendar.noBookingsToday")}</p>
                  ) : (
                    getBookingsForDate(new Date().getDate()).map((booking) => (
                      <div key={booking.id} className="p-3 rounded-lg bg-muted/30 space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">{booking.car}</h4>
                          <Badge className="text-xs">{booking.status}</Badge>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            <span>{booking.client}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{booking.time}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">{t("calendar.quickStats")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{t("calendar.thisMonth")}</span>
                  <span className="font-bold">24 {t("nav.activeBookings").toLowerCase()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{t("calendar.revenue")}</span>
                  <span className="font-bold text-primary">€2,840</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{t("calendar.availableCars")}</span>
                  <span className="font-bold text-green-600">18</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Booking Dialog */}
        <Dialog open={isBookingDialogOpen} onOpenChange={setIsBookingDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{t("booking.title")}</DialogTitle>
              <p className="text-sm text-muted-foreground">
                {selectedStartDate &&
                  selectedEndDate &&
                  `${monthNames[currentDate.getMonth()]} ${selectedStartDate} - ${selectedEndDate} (${calculateDuration()} ${t("time.days")})`}
              </p>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="client">{t("booking.clientName")}</Label>
                  <Input
                    id="client"
                    value={newBooking.client}
                    onChange={(e) => setNewBooking({ ...newBooking, client: e.target.value })}
                    placeholder={t("booking.clientName")}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="car">{t("booking.selectCar")}</Label>
                  <Select
                    value={newBooking.car}
                    onValueChange={(value) => setNewBooking({ ...newBooking, car: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("booking.selectCar")} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCars.map((car) => (
                        <SelectItem key={car.id} value={car.id}>
                          {car.name} - €{car.rate}/{t("time.days")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="time">{t("booking.time")}</Label>
                  <Input
                    id="time"
                    value={newBooking.time}
                    onChange={(e) => setNewBooking({ ...newBooking, time: e.target.value })}
                    placeholder="09:00-17:00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">{t("booking.location")}</Label>
                  <Input
                    id="location"
                    value={newBooking.location}
                    onChange={(e) => setNewBooking({ ...newBooking, location: e.target.value })}
                    placeholder={t("booking.location")}
                  />
                </div>
              </div>

              {calculateTotal() > 0 && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{t("booking.totalAmount")}:</span>
                    <span className="text-2xl font-bold text-primary">€{calculateTotal()}</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {calculateDuration()} {t("time.days")} × €
                    {availableCars.find((c) => c.id === newBooking.car)?.rate || 0}/{t("time.days")}
                  </p>
                </div>
              )}

              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsBookingDialogOpen(false)}>
                  {t("common.cancel")}
                </Button>
                <Button onClick={handleCreateBooking} className="gradient-bg">
                  {t("booking.create")}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
