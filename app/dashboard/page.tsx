"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { Car, Clock, DollarSign, FileText, BookOpen, UserCheck, TrendingUp, Activity } from "lucide-react"

export default function DashboardPage() {
  const { t } = useLanguage()

  const stats = [
    {
      title: t("dashboard.totalCars"),
      value: "24",
      icon: Car,
      description: "Available for rent",
      trend: "+12%",
      color: "from-blue-500 to-blue-600",
    },
    {
      title: t("dashboard.activeBookings"),
      value: "12",
      icon: Clock,
      description: "Currently active",
      trend: "+8%",
      color: "from-green-500 to-green-600",
    },
    {
      title: t("dashboard.totalRevenue"),
      value: "€15,420",
      icon: DollarSign,
      description: "This month",
      trend: "+23%",
      color: "from-purple-500 to-purple-600",
    },
    {
      title: t("dashboard.pendingReports"),
      value: "3",
      icon: FileText,
      description: "Require attention",
      trend: "-2",
      color: "from-orange-500 to-orange-600",
    },
  ]

  const quickActions = [
    { icon: BookOpen, label: "New Booking", color: "from-blue-500 to-blue-600" },
    { icon: Car, label: "Add Car", color: "from-green-500 to-green-600" },
    { icon: FileText, label: "Damage Report", color: "from-orange-500 to-orange-600" },
    { icon: UserCheck, label: "New Client", color: "from-purple-500 to-purple-600" },
  ]

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader title="Dashboard" subtitle="Overview of your rental business performance" />

      <div className="flex-1 p-6 space-y-8 bg-gradient-to-br from-background to-muted/20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="card-hover border-0 shadow-lg bg-gradient-to-br from-card to-card/80">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-muted-foreground">{stat.title}</CardTitle>
                <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.color} shadow-lg`}>
                  <stat.icon className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-3xl font-bold">{stat.value}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">{stat.description}</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    {stat.trend}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl font-bold">Recent Bookings</CardTitle>
                    <CardDescription>Latest car rental activities</CardDescription>
                  </div>
                  <Activity className="h-5 w-5 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((item) => (
                    <div
                      key={item}
                      className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 card-hover"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                          <Car className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold">BMW X5 - Client #{item}</p>
                          <p className="text-sm text-muted-foreground">{new Date().toLocaleDateString()} - 3 days</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg">€{120 + item * 10}</p>
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <p className="text-sm text-green-600 font-medium">Active</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold">Quick Actions</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="h-20 flex flex-col gap-2 modern-button border-0 bg-gradient-to-br from-card to-muted/30 hover:shadow-lg"
                    >
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${action.color} shadow-lg`}>
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-xs font-medium">{action.label}</span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
