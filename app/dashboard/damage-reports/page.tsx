"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import {
  FileText,
  Search,
  Plus,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  Camera,
  Car,
} from "lucide-react"
import { useState } from "react"

export default function DamageReportsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const damageReports = [
    {
      id: 1,
      reportNumber: "DR-001",
      car: { make: "BMW", model: "X5", plate: "ABC-123" },
      client: "<PERSON>",
      damageType: "Scratch",
      severity: "Minor",
      location: "Front bumper",
      reportedDate: "Jan 15",
      status: "pending",
      estimatedCost: 150,
      actualCost: null,
      images: 3,
    },
    {
      id: 2,
      reportNumber: "DR-002",
      car: { make: "Mercedes", model: "C-Class", plate: "DEF-456" },
      client: "Jane Smith",
      damageType: "Dent",
      severity: "Moderate",
      location: "Driver door",
      reportedDate: "Jan 14",
      status: "in-repair",
      estimatedCost: 320,
      actualCost: 285,
      images: 5,
    },
    {
      id: 3,
      reportNumber: "DR-003",
      car: { make: "Audi", model: "A4", plate: "GHI-789" },
      client: "Mike Johnson",
      damageType: "Crack",
      severity: "Major",
      location: "Windshield",
      reportedDate: "Jan 13",
      status: "completed",
      estimatedCost: 450,
      actualCost: 420,
      images: 4,
    },
    {
      id: 4,
      reportNumber: "DR-004",
      car: { make: "Toyota", model: "Camry", plate: "JKL-012" },
      client: "Sarah Wilson",
      damageType: "Scratch",
      severity: "Minor",
      location: "Rear bumper",
      reportedDate: "Jan 12",
      status: "pending",
      estimatedCost: 120,
      actualCost: null,
      images: 2,
    },
    {
      id: 5,
      reportNumber: "DR-005",
      car: { make: "VW", model: "Golf", plate: "MNO-345" },
      client: "David Brown",
      damageType: "Dent",
      severity: "Minor",
      location: "Side panel",
      reportedDate: "Jan 11",
      status: "completed",
      estimatedCost: 200,
      actualCost: 180,
      images: 3,
    },
  ]

  const filteredReports = damageReports.filter((report) => {
    const matchesSearch =
      report.reportNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.car.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.car.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.damageType.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || report.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "in-repair":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "Minor":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "Moderate":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "Major":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader title={t("nav.damageReports")} subtitle="Compact damage tracking system" showActions={false} />

      <div className="flex-1 p-6 space-y-4 bg-gradient-to-br from-background to-muted/20">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { label: "Total", value: "24", icon: FileText, color: "text-blue-600" },
            { label: "Pending", value: "8", icon: Clock, color: "text-yellow-600" },
            { label: "In Repair", value: "5", icon: AlertTriangle, color: "text-orange-600" },
            { label: "Completed", value: "11", icon: CheckCircle, color: "text-green-600" },
          ].map((stat, index) => (
            <Card key={index} className="p-3 border-0 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-lg font-bold ${stat.color}`}>{stat.value}</p>
                  <p className="text-xs text-muted-foreground">{stat.label}</p>
                </div>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <div className="flex items-center gap-3">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search reports..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9 bg-muted/50 border-0"
            />
          </div>
          <div className="flex gap-2">
            {["all", "pending", "in-repair", "completed"].map((status) => (
              <Button
                key={status}
                variant={statusFilter === status ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter(status)}
                className="h-9 px-3 text-xs capitalize"
              >
                {status === "all" ? "All" : status.replace("-", " ")}
              </Button>
            ))}
          </div>
          <Button className="gradient-bg h-9 px-3 text-xs">
            <Plus className="h-4 w-4 mr-1" />
            New Report
          </Button>
        </div>

        {/* Compact Reports Table */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Damage Reports ({filteredReports.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/30">
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">REPORT</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">CAR</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">CLIENT</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">DAMAGE</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">SEVERITY</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">STATUS</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">COST</th>
                    <th className="text-left p-3 text-xs font-medium text-muted-foreground">ACTIONS</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredReports.map((report, index) => (
                    <tr
                      key={report.id}
                      className={`border-b hover:bg-muted/20 transition-colors ${
                        index % 2 === 0 ? "bg-background" : "bg-muted/10"
                      }`}
                    >
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm">{report.reportNumber}</p>
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {report.reportedDate}
                          </p>
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm flex items-center gap-1">
                            <Car className="h-3 w-3" />
                            {report.car.make} {report.car.model}
                          </p>
                          <p className="text-xs text-muted-foreground">{report.car.plate}</p>
                        </div>
                      </td>
                      <td className="p-3">
                        <p className="text-sm flex items-center gap-1">
                          <User className="h-3 w-3 text-muted-foreground" />
                          {report.client}
                        </p>
                      </td>
                      <td className="p-3">
                        <div>
                          <p className="font-medium text-sm">{report.damageType}</p>
                          <p className="text-xs text-muted-foreground">{report.location}</p>
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge className={`${getSeverityColor(report.severity)} text-xs`}>{report.severity}</Badge>
                      </td>
                      <td className="p-3">
                        <Badge className={`${getStatusColor(report.status)} text-xs`}>
                          {report.status.replace("-", " ")}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <div>
                          <p className="font-bold text-sm text-primary">€{report.estimatedCost}</p>
                          {report.actualCost && <p className="text-xs text-green-600">Actual: €{report.actualCost}</p>}
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Camera className="h-3 w-3" />
                            {report.images}
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
