"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import {
  UserCheck,
  Search,
  Plus,
  Filter,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Car,
  Eye,
  Edit,
  MoreHorizontal,
  Star,
  TrendingUp,
} from "lucide-react"
import { useState } from "react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function ClientsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const clients = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+355 69 123 4567",
      address: "Rruga e Kavajes, Tirana, Albania",
      joinDate: "2023-06-15",
      status: "active",
      totalBookings: 12,
      totalSpent: 1440,
      lastBooking: "2024-01-15",
      rating: 4.8,
      preferredCars: ["BMW X5", "Mercedes C-Class"],
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+355 69 987 6543",
      address: "Blloku, Tirana, Albania",
      joinDate: "2023-08-22",
      status: "active",
      totalBookings: 8,
      totalSpent: 960,
      lastBooking: "2024-01-12",
      rating: 4.9,
      preferredCars: ["Audi A4", "Toyota Camry"],
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      name: "Mike Johnson",
      email: "<EMAIL>",
      phone: "+355 69 555 1234",
      address: "Kombinat, Tirana, Albania",
      joinDate: "2023-04-10",
      status: "vip",
      totalBookings: 25,
      totalSpent: 3200,
      lastBooking: "2024-01-10",
      rating: 5.0,
      preferredCars: ["BMW X5", "Porsche Cayenne"],
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 4,
      name: "Sarah Wilson",
      email: "<EMAIL>",
      phone: "+355 69 444 5678",
      address: "Pazari i Ri, Tirana, Albania",
      joinDate: "2023-11-05",
      status: "active",
      totalBookings: 5,
      totalSpent: 480,
      lastBooking: "2024-01-08",
      rating: 4.6,
      preferredCars: ["Volkswagen Golf", "Ford Focus"],
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 5,
      name: "David Brown",
      email: "<EMAIL>",
      phone: "+355 69 333 9876",
      address: "Qender, Tirana, Albania",
      joinDate: "2023-09-18",
      status: "inactive",
      totalBookings: 3,
      totalSpent: 240,
      lastBooking: "2023-12-20",
      rating: 4.2,
      preferredCars: ["Toyota Camry"],
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 6,
      name: "Lisa Anderson",
      email: "<EMAIL>",
      phone: "+355 69 222 5432",
      address: "Artificial Lake, Tirana, Albania",
      joinDate: "2023-07-30",
      status: "vip",
      totalBookings: 18,
      totalSpent: 2160,
      lastBooking: "2024-01-14",
      rating: 4.9,
      preferredCars: ["Mercedes E-Class", "BMW 3 Series"],
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const filteredClients = clients.filter((client) => {
    const matchesSearch =
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.phone.includes(searchTerm)

    const matchesStatus = statusFilter === "all" || client.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "vip":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      case "inactive":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "vip":
        return <Star className="h-3 w-3" />
      case "active":
        return <TrendingUp className="h-3 w-3" />
      default:
        return null
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader
        title={t("nav.clients")}
        subtitle="Manage your customer database and relationships"
        showActions={false}
      />

      <div className="flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Clients</p>
                  <p className="text-2xl font-bold">156</p>
                </div>
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <UserCheck className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Clients</p>
                  <p className="text-2xl font-bold">124</p>
                </div>
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">VIP Clients</p>
                  <p className="text-2xl font-bold">18</p>
                </div>
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Star className="h-5 w-5 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Avg. Rating</p>
                  <p className="text-2xl font-bold">4.7</p>
                </div>
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                  <Star className="h-5 w-5 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 min-w-[300px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clients by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted/50 border-0"
              />
            </div>
            <div className="flex items-center gap-2">
              {["all", "active", "vip", "inactive"].map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className="modern-button capitalize"
                >
                  {status === "all" ? "All" : status}
                </Button>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="modern-button">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button className="modern-button gradient-bg">
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </Button>
          </div>
        </div>

        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <Card key={client.id} className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                      <AvatarImage src={client.avatar || "/placeholder.svg"} />
                      <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-white font-semibold">
                        {client.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-bold text-lg">{client.name}</h3>
                      <div className="flex items-center gap-1">
                        <Badge className={getStatusColor(client.status)}>
                          {getStatusIcon(client.status)}
                          <span className="ml-1">{client.status}</span>
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Client
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Car className="mr-2 h-4 w-4" />
                        New Booking
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{client.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{client.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{client.address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Joined {client.joinDate}</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-primary">{client.totalBookings}</p>
                    <p className="text-xs text-muted-foreground">Total Bookings</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-primary">€{client.totalSpent}</p>
                    <p className="text-xs text-muted-foreground">Total Spent</p>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="font-medium">{client.rating}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Last booking: {client.lastBooking}</span>
                </div>

                <div className="pt-2">
                  <p className="text-xs text-muted-foreground mb-1">Preferred Cars:</p>
                  <div className="flex flex-wrap gap-1">
                    {client.preferredCars.map((car, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {car}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
