"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { useClients } from "@/hooks/use-clients"
import { getStatusColor, getStatusIcon, formatCurrency, formatDate, getClientInitials, Client } from "@/lib/clients"
import {
  UserCheck,
  Search,
  Plus,
  Filter,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Car,
  Eye,
  Edit,
  MoreHorizontal,
  Star,
  TrendingUp,
  Loader2,
  AlertCircle,
} from "lucide-react"
import { useState, useEffect, useMemo } from "react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AddClientDialog } from "@/components/clients/add-client-dialog"
import { FilterDialog } from "@/components/clients/filter-dialog"
import { EditClientDialog } from "@/components/clients/edit-client-dialog"


export default function ClientsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  // Use the clients hook for database integration
  const {
    clients,
    loading,
    error,
    pagination,
    setFilters,
    refreshClients,
    filters
  } = useClients({
    limit: 50,
    offset: 0
  })

  // Update filters when search term or status changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters({
        search: searchTerm || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
        limit: 50,
        offset: 0
      })
    }, 300) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [searchTerm, statusFilter]) // Remove setFilters from dependencies

  // Memoize calculated statistics to prevent unnecessary recalculations
  const statistics = useMemo(() => {
    const totalClients = pagination.total
    const activeClients = clients.filter(c => c.status === 'active').length
    const vipClients = clients.filter(c => c.status === 'vip').length
    const avgRating = clients.length > 0
      ? (clients.reduce((sum, c) => sum + Number(c.rating), 0) / clients.length).toFixed(1)
      : '0.0'

    return { totalClients, activeClients, vipClients, avgRating }
  }, [clients, pagination.total])

  // No need for local filtering since it's handled by the API
  const filteredClients = clients

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader
        title={t("nav.clients")}
        subtitle="Manage your customer database and relationships"
        showActions={false}
      />

      <div className="flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20">
        {/* Error State */}
        {error && (
          <Alert className="border-red-200 bg-red-50 dark:bg-red-900/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
              <Button
                variant="outline"
                size="sm"
                onClick={refreshClients}
                className="ml-2"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Clients</p>
                  <p className="text-2xl font-bold">
                    {loading ? <Loader2 className="h-6 w-6 animate-spin" /> : statistics.totalClients}
                  </p>
                </div>
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <UserCheck className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Clients</p>
                  <p className="text-2xl font-bold">
                    {loading ? <Loader2 className="h-6 w-6 animate-spin" /> : statistics.activeClients}
                  </p>
                </div>
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">VIP Clients</p>
                  <p className="text-2xl font-bold">
                    {loading ? <Loader2 className="h-6 w-6 animate-spin" /> : statistics.vipClients}
                  </p>
                </div>
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Star className="h-5 w-5 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="card-hover border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Avg. Rating</p>
                  <p className="text-2xl font-bold">
                    {loading ? <Loader2 className="h-6 w-6 animate-spin" /> : statistics.avgRating}
                  </p>
                </div>
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                  <Star className="h-5 w-5 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 min-w-[300px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clients by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted/50 border-0"
              />
            </div>
            <div className="flex items-center gap-2">
              {["all", "active", "vip", "inactive"].map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className="modern-button capitalize"
                >
                  {status === "all" ? "All" : status}
                </Button>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <FilterDialog
              currentFilters={filters}
              onFiltersChange={setFilters}
            >
              <Button variant="outline" size="sm" className="modern-button">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </FilterDialog>

            <AddClientDialog>
              <Button className="modern-button gradient-bg">
                <Plus className="h-4 w-4 mr-2" />
                Add Client
              </Button>
            </AddClientDialog>
          </div>
        </div>

        {/* Clients Grid */}
        {loading && clients.length === 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="card-hover border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                        <div className="h-3 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredClients.map((client) => (
              <Card key={client.id} className="card-hover border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                        <AvatarImage src={client.avatar || "/placeholder.svg"} />
                        <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-white font-semibold">
                          {getClientInitials(client.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-bold text-lg">{client.name}</h3>
                        <div className="flex items-center gap-1">
                          <Badge className={getStatusColor(client.status)}>
                            {getStatusIcon(client.status)}
                            <span className="ml-1 capitalize">{client.status}</span>
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          setEditingClient(client)
                          setEditDialogOpen(true)
                        }}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Client
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Car className="mr-2 h-4 w-4" />
                        New Booking
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{client.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{client.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{client.address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Joined {formatDate(client.joinDate)}</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-primary">{client.totalBookings}</p>
                    <p className="text-xs text-muted-foreground">Total Bookings</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-primary">{formatCurrency(client.totalSpent)}</p>
                    <p className="text-xs text-muted-foreground">Total Spent</p>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="font-medium">{Number(client.rating).toFixed(1)}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    Last booking: {client.lastBooking ? formatDate(client.lastBooking) : 'Never'}
                  </span>
                </div>

                <div className="pt-2">
                  <p className="text-xs text-muted-foreground mb-1">Preferred Cars:</p>
                  <div className="flex flex-wrap gap-1">
                    {client.preferredCars && client.preferredCars.length > 0 ? (
                      client.preferredCars.map((car, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {car}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-xs text-muted-foreground">No preferences set</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        )}
      </div>

      {/* Edit Client Dialog */}
      {editingClient && (
        <EditClientDialog
          client={editingClient}
          onClientUpdated={refreshClients}
          open={editDialogOpen}
          onOpenChange={(open) => {
            setEditDialogOpen(open)
            if (!open) {
              setEditingClient(null)
            }
          }}
        />
      )}
    </div>
  )
}
