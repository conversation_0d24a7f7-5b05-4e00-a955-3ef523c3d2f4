"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { Plus, Search, Edit, Trash2, Fuel, Users, Calendar, Filter, Grid, List } from "lucide-react"
import { useState } from "react"

export default function CarsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedModel, setSelectedModel] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  const carBrands = {
    BMW: "https://logos-world.net/wp-content/uploads/2020/03/BMW-Logo.png",
    Mercedes: "https://logos-world.net/wp-content/uploads/2020/04/Mercedes-Logo.png",
    Audi: "https://logos-world.net/wp-content/uploads/2020/04/Audi-Logo.png",
    Toyota: "https://logos-world.net/wp-content/uploads/2020/03/Toyota-Logo.png",
    Volkswagen: "https://logos-world.net/wp-content/uploads/2020/04/Volkswagen-Logo.png",
    Ford: "https://logos-world.net/wp-content/uploads/2020/04/Ford-Logo.png",
    Tesla: "https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png",
    Porsche: "https://logos-world.net/wp-content/uploads/2020/04/Porsche-Logo.png",
    Lamborghini: "https://logos-world.net/wp-content/uploads/2020/04/Lamborghini-Logo.png",
    Ferrari: "https://logos-world.net/wp-content/uploads/2020/04/Ferrari-Logo.png",
    Bentley: "https://logos-world.net/wp-content/uploads/2020/04/Bentley-Logo.png",
    "Rolls-Royce": "https://logos-world.net/wp-content/uploads/2020/04/Rolls-Royce-Logo.png",
  }

  const carModels = {
    BMW: ["X5", "X3", "3 Series", "5 Series", "7 Series", "i4", "iX"],
    Mercedes: ["C-Class", "E-Class", "S-Class", "GLC", "GLE", "A-Class", "CLA"],
    Audi: ["A4", "A6", "A8", "Q5", "Q7", "e-tron", "RS6"],
    Toyota: ["Camry", "Corolla", "RAV4", "Highlander", "Prius", "Land Cruiser"],
    Volkswagen: ["Golf", "Passat", "Tiguan", "Touareg", "ID.4", "Arteon"],
    Ford: ["Focus", "Mustang", "Explorer", "F-150", "Escape", "Bronco"],
    Tesla: ["Model 3", "Model S", "Model X", "Model Y"],
    Porsche: ["911", "Cayenne", "Macan", "Panamera", "Taycan"],
    Lamborghini: ["Huracán", "Aventador", "Urus"],
    Ferrari: ["488", "F8", "Roma", "Portofino"],
    Bentley: ["Continental", "Bentayga", "Flying Spur"],
    "Rolls-Royce": ["Ghost", "Phantom", "Cullinan"],
  }

  const cars = [
    {
      id: 1,
      make: "BMW",
      model: "X5",
      year: 2023,
      plate: "ABC-123",
      status: "Available",
      dailyRate: 120,
      fuelType: "Petrol",
      seats: 5,
      transmission: "Auto",
      mileage: "15,000",
    },
    {
      id: 2,
      make: "Mercedes",
      model: "C-Class",
      year: 2022,
      plate: "DEF-456",
      status: "Rented",
      dailyRate: 100,
      fuelType: "Diesel",
      seats: 5,
      transmission: "Auto",
      mileage: "22,000",
    },
    {
      id: 3,
      make: "Audi",
      model: "A4",
      year: 2023,
      plate: "GHI-789",
      status: "Maintenance",
      dailyRate: 110,
      fuelType: "Petrol",
      seats: 5,
      transmission: "Manual",
      mileage: "8,500",
    },
    {
      id: 4,
      make: "Tesla",
      model: "Model 3",
      year: 2023,
      plate: "STU-901",
      status: "Available",
      dailyRate: 140,
      fuelType: "Electric",
      seats: 5,
      transmission: "Auto",
      mileage: "5,000",
    },
    {
      id: 5,
      make: "Porsche",
      model: "Cayenne",
      year: 2023,
      plate: "VWX-234",
      status: "Available",
      dailyRate: 200,
      fuelType: "Petrol",
      seats: 5,
      transmission: "Auto",
      mileage: "3,000",
    },
    {
      id: 6,
      make: "Lamborghini",
      model: "Huracán",
      year: 2023,
      plate: "LMB-001",
      status: "Available",
      dailyRate: 500,
      fuelType: "Petrol",
      seats: 2,
      transmission: "Auto",
      mileage: "1,200",
    },
  ]

  const brands = Object.keys(carBrands)
  const availableModels = selectedBrand === "all" ? [] : carModels[selectedBrand as keyof typeof carModels] || []

  const filteredCars = cars.filter((car) => {
    const matchesSearch =
      car.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      car.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      car.plate.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesBrand = selectedBrand === "all" || car.make === selectedBrand
    const matchesModel = selectedModel === "all" || car.model === selectedModel

    return matchesSearch && matchesBrand && matchesModel
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Available":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "Rented":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "Maintenance":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader title={t("nav.cars")} subtitle="Compact fleet management" showActions={false} />

      <div className="flex-1 p-6 space-y-4 bg-gradient-to-br from-background to-muted/20">
        {/* Brand Logos */}
        <div className="flex items-center gap-2 overflow-x-auto pb-2">
          <Button
            variant={selectedBrand === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setSelectedBrand("all")
              setSelectedModel("all")
            }}
            className="whitespace-nowrap h-8 px-3 text-xs"
          >
            All Brands
          </Button>
          {brands.map((brand) => (
            <Button
              key={brand}
              variant={selectedBrand === brand ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setSelectedBrand(brand)
                setSelectedModel("all")
              }}
              className="whitespace-nowrap h-8 px-2 flex items-center gap-2"
            >
              <img
                src={carBrands[brand as keyof typeof carBrands] || "/placeholder.svg"}
                alt={brand}
                className="w-4 h-4 object-contain"
              />
              <span className="text-xs">{brand}</span>
            </Button>
          ))}
        </div>

        {/* Models (if brand selected) */}
        {selectedBrand !== "all" && availableModels.length > 0 && (
          <div className="flex items-center gap-2 overflow-x-auto pb-2">
            <Button
              variant={selectedModel === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedModel("all")}
              className="whitespace-nowrap h-7 px-2 text-xs"
            >
              All Models
            </Button>
            {availableModels.map((model) => (
              <Button
                key={model}
                variant={selectedModel === model ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedModel(model)}
                className="whitespace-nowrap h-7 px-2 text-xs"
              >
                {model}
              </Button>
            ))}
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search cars..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9 bg-muted/50 border-0"
              />
            </div>
            <div className="flex gap-1">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-9 px-3"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-9 px-3"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="h-9 px-3 text-xs">
              <Filter className="h-4 w-4 mr-1" />
              Filter
            </Button>
            <Button className="gradient-bg h-9 px-3 text-xs">
              <Plus className="h-4 w-4 mr-1" />
              Add Car
            </Button>
          </div>
        </div>

        {/* Cars Display */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
            {filteredCars.map((car) => (
              <Card key={car.id} className="overflow-hidden card-hover border-0 shadow-sm">
                <div className="aspect-video relative bg-gradient-to-br from-muted/30 to-muted/60">
                  <Badge className={`absolute top-2 right-2 text-xs ${getStatusColor(car.status)}`}>{car.status}</Badge>
                  <div className="absolute top-2 left-2 w-8 h-8 bg-white rounded-lg p-1 shadow-sm">
                    <img
                      src={carBrands[car.make as keyof typeof carBrands] || "/placeholder.svg"}
                      alt={car.make}
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <div className="absolute bottom-2 left-2 right-2">
                    <h3 className="font-bold text-sm text-white drop-shadow-lg">
                      {car.make} {car.model}
                    </h3>
                    <p className="text-xs text-white/80 drop-shadow">
                      {car.year} • {car.plate}
                    </p>
                  </div>
                </div>
                <CardContent className="p-3">
                  <div className="space-y-2">
                    <div className="grid grid-cols-3 gap-1 text-xs">
                      <div className="flex items-center gap-1">
                        <Fuel className="h-3 w-3 text-muted-foreground" />
                        <span>{car.fuelType}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3 text-muted-foreground" />
                        <span>{car.seats}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span>{car.transmission}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-primary">€{car.dailyRate}/day</span>
                      <div className="flex gap-1">
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/30">
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">CAR</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">DETAILS</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">STATUS</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">RATE</th>
                      <th className="text-left p-3 text-xs font-medium text-muted-foreground">ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCars.map((car, index) => (
                      <tr
                        key={car.id}
                        className={`border-b hover:bg-muted/20 transition-colors ${
                          index % 2 === 0 ? "bg-background" : "bg-muted/10"
                        }`}
                      >
                        <td className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-white rounded-lg p-1 shadow-sm">
                              <img
                                src={carBrands[car.make as keyof typeof carBrands] || "/placeholder.svg"}
                                alt={car.make}
                                className="w-full h-full object-contain"
                              />
                            </div>
                            <div>
                              <p className="font-medium text-sm">
                                {car.make} {car.model}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {car.year} • {car.plate}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="text-xs space-y-1">
                            <div className="flex items-center gap-1">
                              <Fuel className="h-3 w-3" />
                              {car.fuelType}
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {car.seats} seats • {car.transmission}
                            </div>
                            <div>{car.mileage} km</div>
                          </div>
                        </td>
                        <td className="p-3">
                          <Badge className={`${getStatusColor(car.status)} text-xs`}>{car.status}</Badge>
                        </td>
                        <td className="p-3">
                          <span className="font-bold text-primary">€{car.dailyRate}/day</span>
                        </td>
                        <td className="p-3">
                          <div className="flex gap-1">
                            <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
