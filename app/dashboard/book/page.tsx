"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import { BookOpen, Calendar, User, Car, Search, Check, Filter, Fuel, Users, Crown, Truck, Minimize2, Zap, Star } from "lucide-react"
import { useState } from "react"

export default function BookPage() {
  const { t } = useLanguage()
  const [formData, setFormData] = useState({
    clientName: "",
    clientPhone: "",
    carId: "",
    startDate: "",
    endDate: "",
    notes: "",
  })
  const [carSearch, setCarSearch] = useState("")
  const [showCarDropdown, setShow<PERSON>ar<PERSON>ropdown] = useState(false)
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedType, setSelectedType] = useState("all")

  const carBrands = {
    BMW: "https://logos-world.net/wp-content/uploads/2020/03/BMW-Logo.png",
    Mercedes: "https://logos-world.net/wp-content/uploads/2020/04/Mercedes-Logo.png",
    Audi: "https://logos-world.net/wp-content/uploads/2020/04/Audi-Logo.png",
    Toyota: "https://logos-world.net/wp-content/uploads/2020/03/Toyota-Logo.png",
    Volkswagen: "https://logos-world.net/wp-content/uploads/2020/04/Volkswagen-Logo.png",
    Ford: "https://logos-world.net/wp-content/uploads/2020/04/Ford-Logo.png",
    Tesla: "https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png",
    Porsche: "https://logos-world.net/wp-content/uploads/2020/04/Porsche-Logo.png",
  }

  const availableCars = [
    {
      id: "1",
      make: "BMW",
      model: "X5",
      year: 2023,
      rate: 120,
      available: true,
      type: "luxury",
      fuelType: "Petrol",
      seats: 5,
      transmission: "Auto",
    },
    {
      id: "2",
      make: "Mercedes",
      model: "C-Class",
      year: 2022,
      rate: 100,
      available: true,
      type: "luxury",
      fuelType: "Diesel",
      seats: 5,
      transmission: "Auto",
    },
    {
      id: "3",
      make: "Audi",
      model: "A4",
      year: 2023,
      rate: 110,
      available: false,
      type: "sedan",
      fuelType: "Petrol",
      seats: 5,
      transmission: "Manual",
    },
    {
      id: "4",
      make: "Toyota",
      model: "Camry",
      year: 2022,
      rate: 80,
      available: true,
      type: "sedan",
      fuelType: "Hybrid",
      seats: 5,
      transmission: "Auto",
    },
    {
      id: "5",
      make: "BMW",
      model: "3 Series",
      year: 2023,
      rate: 95,
      available: true,
      type: "sedan",
      fuelType: "Petrol",
      seats: 5,
      transmission: "Auto",
    },
    {
      id: "6",
      make: "Tesla",
      model: "Model 3",
      year: 2023,
      rate: 140,
      available: true,
      type: "electric",
      fuelType: "Electric",
      seats: 5,
      transmission: "Auto",
    },
    {
      id: "7",
      make: "Volkswagen",
      model: "Golf",
      year: 2023,
      rate: 70,
      available: true,
      type: "compact",
      fuelType: "Petrol",
      seats: 5,
      transmission: "Manual",
    },
    {
      id: "8",
      make: "Ford",
      model: "Explorer",
      year: 2022,
      rate: 95,
      available: true,
      type: "suv",
      fuelType: "Petrol",
      seats: 7,
      transmission: "Auto",
    },
    {
      id: "9",
      make: "Porsche",
      model: "Cayenne",
      year: 2023,
      rate: 200,
      available: true,
      type: "luxury",
      fuelType: "Petrol",
      seats: 5,
      transmission: "Auto",
    },
  ]

  const carTypes = [
    { id: "all", name: t("common.all"), icon: Filter },
    { id: "luxury", name: t("carType.luxury"), icon: Crown },
    { id: "suv", name: t("carType.suv"), icon: Truck },
    { id: "sedan", name: t("carType.sedan"), icon: Car },
    { id: "compact", name: t("carType.compact"), icon: Minimize2 },
    { id: "electric", name: t("carType.electric"), icon: Zap },
  ]

  const brands = ["all", ...Object.keys(carBrands)]

  const filteredCars = availableCars.filter((car) => {
    const matchesSearch =
      car.make.toLowerCase().includes(carSearch.toLowerCase()) ||
      car.model.toLowerCase().includes(carSearch.toLowerCase()) ||
      `${car.make} ${car.model}`.toLowerCase().includes(carSearch.toLowerCase())

    const matchesBrand = selectedBrand === "all" || car.make === selectedBrand
    const matchesType = selectedType === "all" || car.type === selectedType
    const isAvailable = car.available

    return matchesSearch && matchesBrand && matchesType && isAvailable
  })

  const selectedCar = availableCars.find((car) => car.id === formData.carId)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Booking data:", formData)
    // Handle booking submission
  }

  const calculateTotal = () => {
    if (formData.startDate && formData.endDate && formData.carId) {
      const start = new Date(formData.startDate)
      const end = new Date(formData.endDate)
      const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
      const car = availableCars.find((c) => c.id === formData.carId)
      return days > 0 && car ? days * car.rate : 0
    }
    return 0
  }

  const selectCar = (car: (typeof availableCars)[0]) => {
    setFormData({ ...formData, carId: car.id })
    setCarSearch(`${car.make} ${car.model} ${car.year}`)
    setShowCarDropdown(false)
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader title={t("nav.book")} subtitle={t("booking.title")} showActions={false} />

      <div className="flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  {t("booking.title")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="clientName">{t("booking.clientName")} *</Label>
                      <Input
                        id="clientName"
                        value={formData.clientName}
                        onChange={(e) => setFormData({ ...formData, clientName: e.target.value })}
                        placeholder={t("booking.clientName")}
                        required
                        className="h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="clientPhone">{t("booking.clientPhone")} *</Label>
                      <Input
                        id="clientPhone"
                        value={formData.clientPhone}
                        onChange={(e) => setFormData({ ...formData, clientPhone: e.target.value })}
                        placeholder={t("booking.clientPhone")}
                        required
                        className="h-12"
                      />
                    </div>
                  </div>

                  {/* Smart Car Selection */}
                  <div className="space-y-4">
                    <Label>{t("booking.selectCar")} *</Label>

                    {/* Search and Filters */}
                    <div className="space-y-3">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          value={carSearch}
                          onChange={(e) => {
                            setCarSearch(e.target.value)
                            setShowCarDropdown(true)
                          }}
                          onFocus={() => setShowCarDropdown(true)}
                          placeholder={t("booking.searchCars")}
                          className="pl-10 h-12"
                        />
                      </div>

                      {/* Filter Buttons */}
                      <div className="flex flex-wrap gap-2">
                        <div className="flex items-center gap-2">
                          <Filter className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">{t("booking.filterByType")}:</span>
                          {carTypes.map((type) => {
                            const IconComponent = type.icon
                            return (
                              <Button
                                key={type.id}
                                type="button"
                                variant={selectedType === type.id ? "default" : "outline"}
                                size="sm"
                                onClick={() => setSelectedType(type.id)}
                                className="h-8 px-3 text-xs flex items-center gap-1.5"
                              >
                                <IconComponent className="h-3 w-3" />
                                {type.name}
                              </Button>
                            )
                          })}
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        <div className="flex items-center gap-2">
                          <Star className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">{t("booking.filterByBrand")}:</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {brands.map((brand) => (
                            <Button
                              key={brand}
                              type="button"
                              variant={selectedBrand === brand ? "default" : "outline"}
                              size="sm"
                              onClick={() => setSelectedBrand(brand)}
                              className="h-8 px-3 text-xs flex items-center gap-2"
                            >
                              {brand !== "all" && (
                                <img
                                  src={carBrands[brand as keyof typeof carBrands] || "/placeholder.svg"}
                                  alt={brand}
                                  className="w-4 h-4 object-contain"
                                />
                              )}
                              {brand === "all" ? t("common.all") : brand}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Available Cars Display */}
                    <div className="border rounded-lg p-4 bg-muted/20">
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <Car className="h-4 w-4" />
                        {t("booking.availableCars")} ({filteredCars.length})
                      </h4>

                      {filteredCars.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                          {filteredCars.map((car) => (
                            <div
                              key={car.id}
                              className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                                formData.carId === car.id
                                  ? "border-primary bg-primary/10"
                                  : "border-border hover:border-primary/50"
                              }`}
                              onClick={() => selectCar(car)}
                            >
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <img
                                    src={carBrands[car.make as keyof typeof carBrands] || "/placeholder.svg"}
                                    alt={car.make}
                                    className="w-6 h-6 object-contain"
                                  />
                                  <div>
                                    <p className="font-medium text-sm">
                                      {car.make} {car.model} {car.year}
                                    </p>
                                    <Badge variant="secondary" className="text-xs">
                                      {t(`carType.${car.type}`)}
                                    </Badge>
                                  </div>
                                </div>
                                {formData.carId === car.id && <Check className="h-4 w-4 text-primary" />}
                              </div>

                              <div className="flex items-center justify-between text-xs text-muted-foreground">
                                <div className="flex items-center gap-3">
                                  <div className="flex items-center gap-1">
                                    <Fuel className="h-3 w-3" />
                                    {car.fuelType}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Users className="h-3 w-3" />
                                    {car.seats}
                                  </div>
                                </div>
                                <span className="font-bold text-primary">
                                  €{car.rate}/{t("time.days")}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-center text-muted-foreground py-4">{t("booking.noAvailableCars")}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate">{t("booking.startDate")} *</Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={formData.startDate}
                        onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                        required
                        className="h-12"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="endDate">{t("booking.endDate")} *</Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={formData.endDate}
                        onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                        required
                        className="h-12"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">{t("booking.notes")}</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                      placeholder={t("booking.notes")}
                      className="min-h-[100px]"
                    />
                  </div>

                  <Button type="submit" className="w-full h-12 modern-button gradient-bg text-lg font-semibold">
                    {t("booking.create")}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader>
                <CardTitle>{t("booking.summary")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{formData.clientName || t("booking.clientName")}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Car className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {selectedCar
                      ? `${selectedCar.make} ${selectedCar.model} ${selectedCar.year}`
                      : t("booking.selectCar")}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {formData.startDate && formData.endDate
                      ? `${formData.startDate} - ${formData.endDate}`
                      : t("booking.startDate") + " - " + t("booking.endDate")}
                  </span>
                </div>

                {calculateTotal() > 0 && (
                  <div className="border-t pt-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">{t("booking.dailyRate")}:</span>
                        <span className="font-medium">€{selectedCar?.rate}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">{t("booking.duration")}:</span>
                        <span className="font-medium">
                          {Math.ceil(
                            (new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) /
                              (1000 * 60 * 60 * 24),
                          )}{" "}
                          {t("time.days")}
                        </span>
                      </div>
                      <div className="flex justify-between items-center pt-2 border-t">
                        <span className="font-semibold">{t("booking.totalAmount")}:</span>
                        <span className="text-2xl font-bold text-primary">€{calculateTotal()}</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
