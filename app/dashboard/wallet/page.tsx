"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard-header"
import { useLanguage } from "@/components/language-provider"
import {
  Wallet,
  TrendingUp,
  TrendingDown,
  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  Calendar,
  Filter,
  Download,
} from "lucide-react"
import { useState } from "react"

export default function WalletPage() {
  const { t } = useLanguage()
  const [selectedPeriod, setSelectedPeriod] = useState("month")

  const walletStats = [
    {
      title: "Total Balance",
      value: "€12,450.00",
      change: "+15.2%",
      trend: "up",
      icon: Wallet,
      color: "from-green-500 to-green-600",
    },
    {
      title: "This Month Income",
      value: "€3,280.00",
      change: "+8.1%",
      trend: "up",
      icon: TrendingUp,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "Pending Payments",
      value: "€890.00",
      change: "-2.3%",
      trend: "down",
      icon: CreditCard,
      color: "from-orange-500 to-orange-600",
    },
    {
      title: "Total Expenses",
      value: "€1,120.00",
      change: "+5.7%",
      trend: "up",
      icon: TrendingDown,
      color: "from-red-500 to-red-600",
    },
  ]

  const transactions = [
    {
      id: 1,
      type: "income",
      description: "BMW X5 Rental - John Doe",
      amount: 240.0,
      date: "2024-01-15",
      status: "completed",
      category: "rental",
    },
    {
      id: 2,
      type: "expense",
      description: "Car Maintenance - Mercedes C-Class",
      amount: -85.0,
      date: "2024-01-14",
      status: "completed",
      category: "maintenance",
    },
    {
      id: 3,
      type: "income",
      description: "Audi A4 Rental - Jane Smith",
      amount: 180.0,
      date: "2024-01-13",
      status: "pending",
      category: "rental",
    },
    {
      id: 4,
      type: "expense",
      description: "Insurance Payment",
      amount: -320.0,
      date: "2024-01-12",
      status: "completed",
      category: "insurance",
    },
    {
      id: 5,
      type: "income",
      description: "Toyota Camry Rental - Mike Johnson",
      amount: 160.0,
      date: "2024-01-11",
      status: "completed",
      category: "rental",
    },
    {
      id: 6,
      type: "expense",
      description: "Fuel Costs",
      amount: -45.0,
      date: "2024-01-10",
      status: "completed",
      category: "fuel",
    },
  ]

  const getTransactionIcon = (type: string) => {
    return type === "income" ? ArrowUpRight : ArrowDownLeft
  }

  const getTransactionColor = (type: string) => {
    return type === "income" ? "text-green-600" : "text-red-600"
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "rental":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "maintenance":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"
      case "insurance":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      case "fuel":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <DashboardHeader
        title={t("nav.wallet")}
        subtitle="Track your financial transactions and revenue"
        showActions={false}
      />

      <div className="flex-1 p-6 space-y-6 bg-gradient-to-br from-background to-muted/20">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {walletStats.map((stat, index) => (
            <Card key={index} className="card-hover border-0 shadow-lg bg-gradient-to-br from-card to-card/80">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-muted-foreground">{stat.title}</CardTitle>
                <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.color} shadow-lg`}>
                  <stat.icon className="h-5 w-5 text-white" />
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center gap-1 text-xs font-medium">
                  {stat.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 text-green-600" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-600" />
                  )}
                  <span className={stat.trend === "up" ? "text-green-600" : "text-red-600"}>{stat.change}</span>
                  <span className="text-muted-foreground">vs last month</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Transactions */}
          <div className="lg:col-span-2">
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl font-bold">Recent Transactions</CardTitle>
                    <p className="text-sm text-muted-foreground">Your latest financial activities</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="modern-button">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm" className="modern-button">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions.map((transaction) => {
                    const Icon = getTransactionIcon(transaction.type)
                    return (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 card-hover"
                      >
                        <div className="flex items-center gap-4">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              transaction.type === "income"
                                ? "bg-green-100 dark:bg-green-900/20"
                                : "bg-red-100 dark:bg-red-900/20"
                            }`}
                          >
                            <Icon className={`h-5 w-5 ${getTransactionColor(transaction.type)}`} />
                          </div>
                          <div>
                            <p className="font-semibold">{transaction.description}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <p className="text-sm text-muted-foreground">{transaction.date}</p>
                              <Badge className={getCategoryColor(transaction.category)} variant="secondary">
                                {transaction.category}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`font-bold text-lg ${getTransactionColor(transaction.type)}`}>
                            {transaction.amount > 0 ? "+" : ""}€{Math.abs(transaction.amount).toFixed(2)}
                          </p>
                          <Badge className={getStatusColor(transaction.status)} variant="secondary">
                            {transaction.status}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions & Summary */}
          <div className="space-y-6">
            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full modern-button gradient-bg">
                  <ArrowUpRight className="h-4 w-4 mr-2" />
                  Add Income
                </Button>
                <Button variant="outline" className="w-full modern-button">
                  <ArrowDownLeft className="h-4 w-4 mr-2" />
                  Record Expense
                </Button>
                <Button variant="outline" className="w-full modern-button">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Payment
                </Button>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Monthly Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Income</span>
                  <span className="font-bold text-green-600">€4,280.00</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Expenses</span>
                  <span className="font-bold text-red-600">€1,120.00</span>
                </div>
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Net Profit</span>
                    <span className="font-bold text-xl text-primary">€3,160.00</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
