
services:
  db:
    container_name: postgres_container_rental_or_el
    image: postgres
    restart: always
    environment:
      POSTGRES_USER: administrator
      POSTGRES_PASSWORD: or_el_pass2025
      POSTGRES_DB: rental_or_el_db
    ports:
      - "5432:5432"
  pgadmin:
    container_name: pgadmin4_container_rental_or_el
    image: dpage/pgadmin4
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: d
    ports:
      - "5050:80"